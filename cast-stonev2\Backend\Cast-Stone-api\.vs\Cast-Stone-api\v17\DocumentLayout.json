{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\services\\implementations\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:services\\implementations\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\repositories\\implementations\\productrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:repositories\\implementations\\productrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:data\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\controllers\\productscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:controllers\\productscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\createproductrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\createproductrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\response\\productresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\response\\productresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\mappings\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:mappings\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\updateproductrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\updateproductrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\updatecartitemrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\updatecartitemrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\paymentrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\paymentrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\createproductdetailsrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\createproductdetailsrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\createproductspecificationsrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\createproductspecificationsrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\dtos\\request\\addtocartrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:dtos\\request\\addtocartrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\subscription.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\subscription.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\status.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\status.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\productdetails.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\productdetails.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\product.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\product.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\collection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\collection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\cartitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\cartitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\cart.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\cart.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\controllers\\smtpcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:controllers\\smtpcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\services\\emailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:services\\emailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ProductService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\Implementations\\ProductService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\ProductService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\Implementations\\ProductService.cs", "RelativeToolTip": "Services\\Implementations\\ProductService.cs", "ViewState": "AgIAAFQAAAAAAAAAAAAkwH4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:51:32.016Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ProductRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Repositories\\Implementations\\ProductRepository.cs", "RelativeDocumentMoniker": "Repositories\\Implementations\\ProductRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Repositories\\Implementations\\ProductRepository.cs", "RelativeToolTip": "Repositories\\Implementations\\ProductRepository.cs", "ViewState": "AgIAAMsAAAAAAAAAAAAqwNcAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:50:38.4Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Data\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "Data\\ApplicationDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Data\\ApplicationDbContext.cs", "RelativeToolTip": "Data\\ApplicationDbContext.cs", "ViewState": "AgIAAP0AAAAAAAAAAAAqwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:50:20.813Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ProductsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\ProductsController.cs", "RelativeDocumentMoniker": "Controllers\\ProductsController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\ProductsController.cs", "RelativeToolTip": "Controllers\\ProductsController.cs", "ViewState": "AgIAAM4AAAAAAAAAAAAAAN0AAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:49:10.791Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "User.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\User.cs", "RelativeDocumentMoniker": "Domain\\Models\\User.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\User.cs", "RelativeToolTip": "Domain\\Models\\User.cs", "ViewState": "AgIAABoAAAAAAAAAAAAmwBEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:27:32.636Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ProductResponse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Response\\ProductResponse.cs", "RelativeDocumentMoniker": "DTOs\\Response\\ProductResponse.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Response\\ProductResponse.cs", "RelativeToolTip": "DTOs\\Response\\ProductResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:48:20.792Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "MappingProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Mappings\\MappingProfile.cs", "RelativeDocumentMoniker": "Mappings\\MappingProfile.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Mappings\\MappingProfile.cs", "RelativeToolTip": "Mappings\\MappingProfile.cs", "ViewState": "AgIAAJoAAAAAAAAAAAAgwBUAAABDAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:47:36.923Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "UpdateProductRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\UpdateProductRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\UpdateProductRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\UpdateProductRequest.cs", "RelativeToolTip": "DTOs\\Request\\UpdateProductRequest.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwBYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:47:13.546Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "UpdateCartItemRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\UpdateCartItemRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\UpdateCartItemRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\UpdateCartItemRequest.cs", "RelativeToolTip": "DTOs\\Request\\UpdateCartItemRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:47:09.03Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "CreateProductRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateProductRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\CreateProductRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateProductRequest.cs", "RelativeToolTip": "DTOs\\Request\\CreateProductRequest.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAQwBYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:46:20.108Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "PaymentRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\PaymentRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\PaymentRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\PaymentRequest.cs", "RelativeToolTip": "DTOs\\Request\\PaymentRequest.cs", "ViewState": "AgIAABwAAAAAAAAAAAAQwAoAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:47:00.587Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "CreateProductDetailsRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateProductDetailsRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\CreateProductDetailsRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateProductDetailsRequest.cs", "RelativeToolTip": "DTOs\\Request\\CreateProductDetailsRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:46:42.336Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "CreateProductSpecificationsRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateProductSpecificationsRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\CreateProductSpecificationsRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\CreateProductSpecificationsRequest.cs", "RelativeToolTip": "DTOs\\Request\\CreateProductSpecificationsRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:46:38.988Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "AddToCartRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\AddToCartRequest.cs", "RelativeDocumentMoniker": "DTOs\\Request\\AddToCartRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\DTOs\\Request\\AddToCartRequest.cs", "RelativeToolTip": "DTOs\\Request\\AddToCartRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:46:12.85Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "Subscription.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Subscription.cs", "RelativeDocumentMoniker": "Domain\\Models\\Subscription.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Subscription.cs", "RelativeToolTip": "Domain\\Models\\Subscription.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:46:05.772Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "ProductDetails.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\ProductDetails.cs", "RelativeDocumentMoniker": "Domain\\Models\\ProductDetails.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\ProductDetails.cs", "RelativeToolTip": "Domain\\Models\\ProductDetails.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAwwA4AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:45:55.414Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "Product.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Product.cs", "RelativeDocumentMoniker": "Domain\\Models\\Product.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Product.cs", "RelativeToolTip": "Domain\\Models\\Product.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAgwBsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:42:06.627Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "Collection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Collection.cs", "RelativeDocumentMoniker": "Domain\\Models\\Collection.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Collection.cs", "RelativeToolTip": "Domain\\Models\\Collection.cs", "ViewState": "AgIAACkAAAAAAAAAAAAswA4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:40:19.906Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "CartItem.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\CartItem.cs", "RelativeDocumentMoniker": "Domain\\Models\\CartItem.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\CartItem.cs", "RelativeToolTip": "Domain\\Models\\CartItem.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAgwA4AAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:40:13.805Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "Cart.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Cart.cs", "RelativeDocumentMoniker": "Domain\\Models\\Cart.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Cart.cs", "RelativeToolTip": "Domain\\Models\\Cart.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAABYAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:40:07.155Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "Status.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Status.cs", "RelativeDocumentMoniker": "Domain\\Models\\Status.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Status.cs", "RelativeToolTip": "Domain\\Models\\Status.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:28:04.062Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "appsettings.Production.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.Production.json", "RelativeDocumentMoniker": "appsettings.Production.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.Production.json", "RelativeToolTip": "appsettings.Production.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-21T15:20:38.195Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Properties\\launchSettings.json", "RelativeToolTip": "Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-16T20:41:38.653Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.Development.json", "RelativeDocumentMoniker": "appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.Development.json", "RelativeToolTip": "appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-16T18:59:37.019Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-16T18:58:28.739Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "EmailService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\EmailService.cs", "RelativeDocumentMoniker": "Services\\EmailService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\EmailService.cs", "RelativeToolTip": "Services\\EmailService.cs", "ViewState": "AgIAAMcBAAAAAAAAAAAuwNMBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T18:56:50.26Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "SMTPController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\SMTPController.cs", "RelativeDocumentMoniker": "Controllers\\SMTPController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\SMTPController.cs", "RelativeToolTip": "Controllers\\SMTPController.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAUwCkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T18:56:14.243Z"}]}, {"DockedWidth": 68, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}]}]}]}