"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/our-story/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-combine-values.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCombineMotionValues: () => (/* binding */ useCombineMotionValues)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n\n\n\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.useMotionValue)(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        const scheduleUpdate = () => motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.preRender(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(updateValue);\n        };\n    });\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-computed.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComputed: () => (/* binding */ useComputed)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n\n\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = [];\n    compute();\n    const value = (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__.useCombineMotionValues)(motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = undefined;\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLWNvbXB1dGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUQ7QUFDaUI7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJEQUFtQjtBQUN2QjtBQUNBLGtCQUFrQiwrRUFBc0IsQ0FBQywyREFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBbUI7QUFDdkI7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXGNhc3Qtc3RvbmV2MlxcY2FzdC1zdG9uZXYyXFxGcm9udGVuZFxcY2FzdC1zdG9uZS1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdmFsdWVcXHVzZS1jb21wdXRlZC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29sbGVjdE1vdGlvblZhbHVlcyB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgdXNlQ29tYmluZU1vdGlvblZhbHVlcyB9IGZyb20gJy4vdXNlLWNvbWJpbmUtdmFsdWVzLm1qcyc7XG5cbmZ1bmN0aW9uIHVzZUNvbXB1dGVkKGNvbXB1dGUpIHtcbiAgICAvKipcbiAgICAgKiBPcGVuIHNlc3Npb24gb2YgY29sbGVjdE1vdGlvblZhbHVlcy4gQW55IE1vdGlvblZhbHVlIHRoYXQgY2FsbHMgZ2V0KClcbiAgICAgKiB3aWxsIGJlIHNhdmVkIGludG8gdGhpcyBhcnJheS5cbiAgICAgKi9cbiAgICBjb2xsZWN0TW90aW9uVmFsdWVzLmN1cnJlbnQgPSBbXTtcbiAgICBjb21wdXRlKCk7XG4gICAgY29uc3QgdmFsdWUgPSB1c2VDb21iaW5lTW90aW9uVmFsdWVzKGNvbGxlY3RNb3Rpb25WYWx1ZXMuY3VycmVudCwgY29tcHV0ZSk7XG4gICAgLyoqXG4gICAgICogU3luY2hyb25vdXNseSBjbG9zZSBzZXNzaW9uIG9mIGNvbGxlY3RNb3Rpb25WYWx1ZXMuXG4gICAgICovXG4gICAgY29sbGVjdE1vdGlvblZhbHVlcy5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuZXhwb3J0IHsgdXNlQ29tcHV0ZWQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-motion-value.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValue: () => (/* binding */ useMotionValue)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n\n\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(() => (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-transform.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransform: () => (/* binding */ useTransform)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n/* harmony import */ var _use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-computed.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\");\n\n\n\n\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return (0,_use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__.useComputed)(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.transform)(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(() => []);\n    return (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__.useCombineMotionValues)(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/transform.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transform: () => (/* binding */ transform)\n/* harmony export */ });\n/* harmony import */ var _interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interpolate.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/interpolate.mjs\");\n\n\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = (0,_interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__.interpolate)(inputRange, outputRange, options);\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsNkRBQVc7QUFDcEM7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXGNhc3Qtc3RvbmV2MlxcY2FzdC1zdG9uZXYyXFxGcm9udGVuZFxcY2FzdC1zdG9uZS1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHRyYW5zZm9ybS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW50ZXJwb2xhdGUgfSBmcm9tICcuL2ludGVycG9sYXRlLm1qcyc7XG5cbmZ1bmN0aW9uIHRyYW5zZm9ybSguLi5hcmdzKSB7XG4gICAgY29uc3QgdXNlSW1tZWRpYXRlID0gIUFycmF5LmlzQXJyYXkoYXJnc1swXSk7XG4gICAgY29uc3QgYXJnT2Zmc2V0ID0gdXNlSW1tZWRpYXRlID8gMCA6IC0xO1xuICAgIGNvbnN0IGlucHV0VmFsdWUgPSBhcmdzWzAgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IGlucHV0UmFuZ2UgPSBhcmdzWzEgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IG91dHB1dFJhbmdlID0gYXJnc1syICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBvcHRpb25zID0gYXJnc1szICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBpbnRlcnBvbGF0b3IgPSBpbnRlcnBvbGF0ZShpbnB1dFJhbmdlLCBvdXRwdXRSYW5nZSwgb3B0aW9ucyk7XG4gICAgcmV0dXJuIHVzZUltbWVkaWF0ZSA/IGludGVycG9sYXRvcihpbnB1dFZhbHVlKSA6IGludGVycG9sYXRvcjtcbn1cblxuZXhwb3J0IHsgdHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/our-story/page.tsx":
/*!************************************!*\
  !*** ./src/app/our-story/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ourStory.module.css */ \"(app-pages-browser)/./src/app/our-story/ourStory.module.css\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OurStoryPage = ()=>{\n    _s();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Mouse tracking for 3D effect\n    const mouseX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useMotionValue)(0);\n    const mouseY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useMotionValue)(0);\n    const rotateX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(mouseY, [\n        -300,\n        300\n    ], [\n        10,\n        -10\n    ]);\n    const rotateY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform)(mouseX, [\n        -300,\n        300\n    ], [\n        -10,\n        10\n    ]);\n    const handleMouseMove = (event)=>{\n        const rect = event.currentTarget.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n        mouseX.set(event.clientX - centerX);\n        mouseY.set(event.clientY - centerY);\n    };\n    // Banner images array\n    const bannerImages = [\n        '/images/CollectionBackground.jpg',\n        '/images/CollectionBackground2.jpg',\n        '/images/CollectionBackground3.jpg',\n        '/images/catalog-banner-bg.jpg'\n    ];\n    // Auto-rotate banner images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OurStoryPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"OurStoryPage.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"OurStoryPage.useEffect.interval\": (prevIndex)=>(prevIndex + 1) % bannerImages.length\n                    }[\"OurStoryPage.useEffect.interval\"]);\n                }\n            }[\"OurStoryPage.useEffect.interval\"], 5000); // Change image every 5 seconds\n            return ({\n                \"OurStoryPage.useEffect\": ()=>clearInterval(interval)\n            })[\"OurStoryPage.useEffect\"];\n        }\n    }[\"OurStoryPage.useEffect\"], [\n        bannerImages.length\n    ]);\n    // Timeline data with images\n    const timelineData = [\n        {\n            year: '2010',\n            title: 'Foundation',\n            description: 'Cast Stone was founded with a vision to revolutionize architectural stone design and manufacturing. Our journey began with a commitment to excellence and innovation in every piece we create.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2012',\n            title: 'First Major Project',\n            description: 'Completed our first large-scale commercial project, establishing our reputation in the industry. This milestone project showcased our capabilities and set the foundation for future growth.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2015',\n            title: 'Innovation Breakthrough',\n            description: 'Developed proprietary casting techniques that enhanced durability and aesthetic appeal. Our research and development team achieved breakthrough innovations that set new industry standards.',\n            image: '/images/CollectionBackground3.jpg'\n        },\n        {\n            year: '2018',\n            title: 'International Expansion',\n            description: 'Expanded operations internationally, bringing our expertise to global markets. We established partnerships worldwide and began serving clients across multiple continents.',\n            image: '/images/catalog-banner-bg.jpg'\n        },\n        {\n            year: '2020',\n            title: 'Sustainable Practices',\n            description: 'Implemented eco-friendly manufacturing processes and sustainable material sourcing. Our commitment to environmental responsibility became a cornerstone of our operations.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2023',\n            title: 'Digital Innovation',\n            description: 'Launched advanced digital design tools and virtual consultation services. We embraced technology to enhance customer experience and streamline our design process.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2024',\n            title: 'Industry Leadership',\n            description: 'Recognized as industry leader with over 500 successful projects worldwide. Our dedication to quality and innovation has established us as the premier choice for architectural stone solutions.',\n            image: '/images/CollectionBackground3.jpg'\n        }\n    ];\n    // Scroll to next section\n    const scrollToTimeline = ()=>{\n        const timelineSection = document.getElementById('timeline-section');\n        if (timelineSection) {\n            timelineSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Navigate timeline\n    const navigateTimeline = (direction)=>{\n        if (direction === 'prev' && selectedYear > 0) {\n            setSelectedYear(selectedYear - 1);\n        } else if (direction === 'next' && selectedYear < timelineData.length - 1) {\n            setSelectedYear(selectedYear + 1);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().storyPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().bannerContainer),\n                    children: [\n                        bannerImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthImageContainer),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthLayer),\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 1.2,\n                                            rotateY: -15\n                                        },\n                                        animate: {\n                                            opacity: index === currentImageIndex ? 0.3 : 0,\n                                            scale: index === currentImageIndex ? 1.3 : 1.2,\n                                            rotateY: index === currentImageIndex ? -10 : -15,\n                                            filter: 'blur(8px)'\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            ease: \"easeInOut\"\n                                        },\n                                        style: {\n                                            backgroundImage: \"url(\".concat(image, \")\"),\n                                            zIndex: index === currentImageIndex ? 1 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthLayer),\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 1.1,\n                                            rotateY: -10\n                                        },\n                                        animate: {\n                                            opacity: index === currentImageIndex ? 0.6 : 0,\n                                            scale: index === currentImageIndex ? 1.2 : 1.1,\n                                            rotateY: index === currentImageIndex ? -5 : -10,\n                                            filter: 'blur(4px)'\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            ease: \"easeInOut\",\n                                            delay: 0.2\n                                        },\n                                        style: {\n                                            backgroundImage: \"url(\".concat(image, \")\"),\n                                            zIndex: index === currentImageIndex ? 2 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthLayer),\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 1,\n                                            rotateY: -5\n                                        },\n                                        animate: {\n                                            opacity: index === currentImageIndex ? 1 : 0,\n                                            scale: index === currentImageIndex ? 1.05 : 1,\n                                            rotateY: index === currentImageIndex ? 0 : -5,\n                                            filter: 'blur(0px)'\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            ease: \"easeInOut\",\n                                            delay: 0.4\n                                        },\n                                        style: {\n                                            backgroundImage: \"url(\".concat(image, \")\"),\n                                            zIndex: index === currentImageIndex ? 3 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTextContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h1, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.5\n                                            },\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSubtitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.8\n                                            },\n                                            children: \"In 2010, the world of architectural stone made the discovery of a new brand.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().scrollArrow),\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        delay: 1.2\n                                    },\n                                    onClick: scrollToTimeline,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowIcon),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M7 10L12 15L17 10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowText),\n                                            children: \"Explore Our Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"timeline-section\",\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineBackground),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTitle),\n                                    children: \"TIMELINE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearNavigation),\n                                    children: [\n                                        timelineData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButton), \" \").concat(index === selectedYear ? (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButtonActive) : ''),\n                                                onClick: ()=>setSelectedYear(index),\n                                                children: item.year\n                                            }, item.year, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().moreButton),\n                                            children: [\n                                                \"More\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"16\",\n                                                    height: \"16\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M19 9L12 16L5 9\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContentContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContent),\n                                    initial: {\n                                        opacity: 0,\n                                        x: 50\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImageContainer),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: timelineData[selectedYear].image,\n                                                alt: timelineData[selectedYear].title,\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImage),\n                                                width: 600,\n                                                height: 400,\n                                                style: {\n                                                    objectFit: 'cover'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTextContainer),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineYear),\n                                                    children: timelineData[selectedYear].year\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineItemTitle),\n                                                    children: timelineData[selectedYear].title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineDescription),\n                                                    children: timelineData[selectedYear].description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, selectedYear, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineNavigation),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().prevButton)),\n                                            onClick: ()=>navigateTimeline('prev'),\n                                            disabled: selectedYear === 0,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M15 18L9 12L15 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().nextButton)),\n                                            onClick: ()=>navigateTimeline('next'),\n                                            disabled: selectedYear === timelineData.length - 1,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 18L15 12L9 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().parallaxContainer),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionSection),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionContainer),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/CollectionBackground.jpg\",\n                                            alt: \"Vision of Architectural Excellence\",\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                            width: 600,\n                                            height: 400,\n                                            style: {\n                                                objectFit: 'cover'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                                children: \"A VISION OF ARCHITECTURAL EXCELLENCE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"At the age of fifty, Cast Stone decided to create its own brand, with the idea of pushing watchmaking beyond anything that existed at the time, with a new contemporary approach to horology. We was planning to develop one product: the watch of his dreams, an approach that involved operating with little regard for production costs, which were excessive.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"When released in 2001, this extraordinary timepiece with its ergonomic tonneau case design punctuated with distinctive torque screws and a compelling six-digit price tag, immediately placed the fledgling brand at the highest summit of the entire luxury watch market.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItemReverse)),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                                children: \"INNOVATION THROUGH CRAFTSMANSHIP\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"Our commitment to innovation extends beyond traditional boundaries. Each piece we create represents a perfect fusion of time-honored craftsmanship techniques and cutting-edge technology. This approach allows us to achieve unprecedented levels of precision and aesthetic refinement.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"The result is a collection of architectural stone products that not only meet the most demanding technical specifications but also inspire architects and designers to explore new possibilities in their creative endeavors. Every project becomes a testament to our unwavering pursuit of excellence.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/CollectionBackground2.jpg\",\n                                            alt: \"Innovation Through Craftsmanship\",\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                            width: 600,\n                                            height: 400,\n                                            style: {\n                                                objectFit: 'cover'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteSection),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteContent),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quote),\n                                                    children: \"“For a long time, I wished to create something extraordinary in architectural stone. I wanted to develop a new approach, far removed from traditional manufacturing methods, something totally innovative. My goal was to establish a new standard of excellence within the architectural stone industry, and I was very eager to see what could be achieved!”\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cite\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteAuthor),\n                                                    children: \"CAST STONE FOUNDER\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteTextContent),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteTitle),\n                                                    children: \"THE FRUITION OF DECADES OF EXPERIENCE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteText),\n                                                    children: \"For Cast Stone, this was not an impulsive decision quickly taken; it was the direct fruition of decades of experience gained through diverse architectural projects and luxury material development. Our deep fascination for innovative manufacturing techniques, expertise in material science, and personal passion for architectural excellence combined with our extreme sensitivity to design and functionality, meant that no existing stone products could completely meet our vision for perfection.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OurStoryPage, \"0ojCtS6QVihEVQ2Pta1T72r82ZY=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useMotionValue,\n        framer_motion__WEBPACK_IMPORTED_MODULE_4__.useMotionValue,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_5__.useTransform\n    ];\n});\n_c = OurStoryPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OurStoryPage);\nvar _c;\n$RefreshReg$(_c, \"OurStoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/our-story/page.tsx\n"));

/***/ })

});