{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app", "files": ["static/chunks/_pages-dir-browser_node_modules_next_dist_pages__app_js.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error": {"id": "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error", "files": ["static/chunks/_pages-dir-browser_node_modules_next_dist_pages__error_js.js"]}, "app\\contact\\page.tsx -> wow.js": {"id": "app\\contact\\page.tsx -> wow.js", "files": ["static/chunks/_app-pages-browser_node_modules_wow_js_dist_wow_js.js"]}, "services\\api\\collections\\delete.ts -> ./get": {"id": "services\\api\\collections\\delete.ts -> ./get", "files": []}, "services\\api\\collections\\delete.ts -> ./update": {"id": "services\\api\\collections\\delete.ts -> ./update", "files": []}, "services\\api\\contactForm\\index.ts -> ./get": {"id": "services\\api\\contactForm\\index.ts -> ./get", "files": []}, "services\\api\\contactForm\\index.ts -> ./post": {"id": "services\\api\\contactForm\\index.ts -> ./post", "files": []}, "services\\api\\orders\\delete.ts -> ./get": {"id": "services\\api\\orders\\delete.ts -> ./get", "files": []}, "services\\api\\orders\\delete.ts -> ./update": {"id": "services\\api\\orders\\delete.ts -> ./update", "files": []}, "services\\api\\orders\\post.ts -> ./get": {"id": "services\\api\\orders\\post.ts -> ./get", "files": []}, "services\\api\\orders\\update.ts -> ./get": {"id": "services\\api\\orders\\update.ts -> ./get", "files": []}, "services\\api\\products\\delete.ts -> ./get": {"id": "services\\api\\products\\delete.ts -> ./get", "files": []}, "services\\api\\products\\delete.ts -> ./update": {"id": "services\\api\\products\\delete.ts -> ./update", "files": []}, "services\\api\\users\\delete.ts -> ./get": {"id": "services\\api\\users\\delete.ts -> ./get", "files": []}, "services\\api\\users\\delete.ts -> ./update": {"id": "services\\api\\users\\delete.ts -> ./update", "files": []}, "services\\api\\users\\post.ts -> ./get": {"id": "services\\api\\users\\post.ts -> ./get", "files": []}}