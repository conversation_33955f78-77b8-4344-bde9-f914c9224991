/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/our-story/ourStory.module.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/* Our Story Page Styles - Blue and White Theme */
.ourStory_OurStoryroot__5JRsM {
  --cast-stone-blue: #2563eb;
  --cast-stone-light-blue: #3b82f6;
  --cast-stone-blue-50: #eff6ff;
  --cast-stone-white: #ffffff;
  --cast-stone-dark-text: #1f2937;
  --cast-stone-gray-text: #4b5563;
  --cast-stone-shadow: rgba(37, 99, 235, 0.1);
  --cast-stone-shadow-hover: rgba(37, 99, 235, 0.15);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Page Container */
.ourStory_storyPage__Z_bIm {
  min-height: 100vh;
  background-color: var(--cast-stone-white);
}

/* Section 1: Hero Section */
.ourStory_heroSection__yxdhw {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.ourStory_bannerContainer__l3GlS {
  width: 100%;
  height: 100%;
  position: relative;
  perspective: 1000px;
  perspective-origin: center center;
}

/* 3D Depth Image Container */
.ourStory_depthImageContainer__q6spc {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
}

/* Individual Depth Layers */
.ourStory_depthLayer__ND5bA {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transform-origin: center center;
  will-change: transform, filter, opacity;
  backface-visibility: hidden;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease-out;
}

/* Enhanced depth with different shadows for each layer */
.ourStory_depthLayer__ND5bA:nth-child(1) {
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
}

.ourStory_depthLayer__ND5bA:nth-child(2) {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.ourStory_depthLayer__ND5bA:nth-child(3) {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.ourStory_bannerImage__tkpeR {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.ourStory_heroContent__Ht0fH {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 4rem 2rem;
  z-index: 10;
  pointer-events: none;
}

.ourStory_heroTextContainer__IYr38 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  max-width: 600px;
  margin-left: 5%;
  pointer-events: auto;
}

.ourStory_heroTitle__fJNw2 {
  font-size: 4.5rem;
  font-weight: 300;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  line-height: 1.1;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.ourStory_heroSubtitle__YzphA {
  font-size: 1.4rem;
  font-weight: 300;
  line-height: 1.6;
  color: white;
  opacity: 0.95;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  max-width: 500px;
}

.ourStory_scrollArrow__9Qmcy {
  align-self: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  margin-bottom: 2rem;
}

.ourStory_scrollArrow__9Qmcy:hover {
  transform: translateY(-5px);
}

.ourStory_arrowIcon__O47r9 {
  width: 48px;
  height: 48px;
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: ourStory_bounce__ZYV81 2s infinite;
}

.ourStory_arrowText__n6lkL {
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  opacity: 0.9;
}

@keyframes ourStory_bounce__ZYV81 {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}



/* Section 2: Timeline Section */
.ourStory_timelineSection__muyko {
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6)),
              url('/images/catalog-banner-bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: white;
  padding: 4rem 0;
}

.ourStory_timelineBackground__s69bf {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  height: 100%;
}

.ourStory_timelineHeader__JPVon {
  text-align: center;
  margin-bottom: 4rem;
}

.ourStory_timelineTitle__Z_R4X {
  font-size: 3rem;
  font-weight: 300;
  letter-spacing: 0.2em;
  margin-bottom: 3rem;
  color: white;
}

.ourStory_yearNavigation__MEsUq {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.ourStory_yearButton__8U6Rp {
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.7);
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.ourStory_yearButton__8U6Rp:hover {
  border-color: white;
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.ourStory_yearButtonActive__PXZUl {
  border-color: white;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  border-bottom: 3px solid white;
}

.ourStory_moreButton__aRMCJ {
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.7);
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ourStory_moreButton__aRMCJ:hover {
  border-color: white;
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.ourStory_timelineContentContainer__beGLY {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
}

.ourStory_timelineContent__HgA6Z {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 500px;
}

.ourStory_timelineImageContainer__SZQwo {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.ourStory_timelineImage__SDTuu {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.ourStory_timelineImage__SDTuu:hover {
  transform: scale(1.05);
}

.ourStory_timelineTextContainer__gvffs {
  padding: 2rem;
}

.ourStory_timelineYear__fKxSh {
  font-size: 4rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  line-height: 1;
}

.ourStory_timelineItemTitle__t1Fd6 {
  font-size: 2.5rem;
  font-weight: 300;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.ourStory_timelineDescription__VLFcW {
  font-size: 1.1rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.ourStory_timelineNavigation__0iu60 {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  display: flex;
  gap: 1rem;
}

.ourStory_navButton__gBL9G {
  width: 50px;
  height: 50px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.ourStory_navButton__gBL9G:hover:not(:disabled) {
  border-color: white;
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.ourStory_navButton__gBL9G:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}







/* Parallax Background Container */
.ourStory_parallaxContainer__Byhmg {
  position: relative;
  background-image: url('/images/CollectionBackground.jpg');
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  min-height: 300vh; /* Covers all three sections */
  width: 100%;
  overflow: hidden;
}

/* Overlay to darken background slightly for better text readability */
.ourStory_parallaxContainer__Byhmg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* Section 3: Vision & Innovation Section */
.ourStory_visionSection__epapt {
  background: transparent;
  padding: 6rem 0;
  min-height: 100vh;
  position: relative;
  z-index: 3;
}

.ourStory_visionContainer__DAzal {
  width: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 8rem;
}

.ourStory_blogItem__43b0M {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 500px;
  background: rgba(255, 255, 255, 0.95);
  padding: 4rem 6rem;
  margin: 4rem 0;
  position: relative;
  z-index: 4;
  backdrop-filter: blur(2px);
  border-radius: 0;
  width: 100%;
}

.ourStory_blogItemReverse__APws5 {
  grid-template-columns: 1fr 1fr;
}

.ourStory_blogImageContainer__BsLT9 {
  position: relative;
  overflow: hidden;
  border-radius: 0;
  height: 500px;
}

.ourStory_blogImage__xjPQJ {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.ourStory_blogImage__xjPQJ:hover {
  transform: scale(1.02);
}

.ourStory_blogContent__xmjea {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.ourStory_blogTitle__hDIux {
  font-size: 2.5rem;
  font-weight: 300;
  color: #1a1a1a;
  margin-bottom: 2rem;
  line-height: 1.2;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.ourStory_blogText__cbTWn {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #4a4a4a;
  margin-bottom: 1.5rem;
  text-align: justify;
}

.ourStory_blogText__cbTWn:last-child {
  margin-bottom: 0;
}

/* Quote Section */
.ourStory_quoteSection__rzXee {
  background: rgba(26, 26, 26, 0.95);
  padding: 6rem 0;
  margin: 4rem 0;
  width: 100%;
  position: relative;
  z-index: 4;
  backdrop-filter: blur(2px);
  border-radius: 0;
}

.ourStory_quoteContainer__gTRKL {
  width: 100%;
  margin: 0;
  padding: 0 6rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: center;
  min-height: 500px;
}

.ourStory_quoteContent__mNoOD {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.ourStory_quote__Bobyw {
  font-size: 1.3rem;
  line-height: 1.6;
  color: #ffffff;
  margin: 0 0 2rem 0;
  font-style: italic;
  font-weight: 300;
  text-align: left;
}

.ourStory_quoteAuthor__TPPEI {
  font-size: 1rem;
  color: #00bcd4;
  font-weight: 600;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  font-style: normal;
}

.ourStory_quoteTextContent__SoG_L {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.ourStory_quoteTitle__Zx8la {
  font-size: 2.5rem;
  font-weight: 300;
  color: #ffffff;
  margin-bottom: 2rem;
  line-height: 1.2;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.ourStory_quoteText__pOHk4 {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #cccccc;
  text-align: justify;
}
/* Responsive Design */
@media (max-width: 1024px) {
  .ourStory_heroTitle__fJNw2 {
    font-size: 3.5rem;
  }

  .ourStory_heroSubtitle__YzphA {
    font-size: 1.2rem;
  }

  .ourStory_heroTextContainer__IYr38 {
    margin-left: 3%;
  }

  .ourStory_timelineContent__HgA6Z {
    gap: 2rem;
  }

  .ourStory_timelineTitle__Z_R4X {
    font-size: 2.5rem;
  }

  .ourStory_timelineItemTitle__t1Fd6 {
    font-size: 2rem;
  }

  .ourStory_timelineYear__fKxSh {
    font-size: 3rem;
  }

  .ourStory_visionContainer__DAzal {
    gap: 6rem;
  }

  .ourStory_blogItem__43b0M {
    gap: 3rem;
    min-height: 400px;
    padding: 3rem 4rem;
  }

  .ourStory_blogImageContainer__BsLT9 {
    height: 400px;
  }

  .ourStory_blogTitle__hDIux {
    font-size: 2rem;
  }

  .ourStory_blogText__cbTWn {
    font-size: 1rem;
  }

  .ourStory_quoteContainer__gTRKL {
    gap: 4rem;
  }

  .ourStory_quoteTitle__Zx8la {
    font-size: 2rem;
  }

  .ourStory_quote__Bobyw {
    font-size: 1.2rem;
  }

  .ourStory_quoteText__pOHk4 {
    font-size: 1rem;
  }

  /* Disable parallax on smaller screens for better performance */
  .ourStory_parallaxContainer__Byhmg {
    background-attachment: scroll;
  }

  /* Reduce 3D effects on smaller screens */
  .ourStory_bannerContainer__l3GlS {
    perspective: 500px;
  }

  .ourStory_depthLayer__ND5bA {
    border-radius: 8px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }

  .ourStory_depthLayer__ND5bA:nth-child(1) {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  }

  .ourStory_depthLayer__ND5bA:nth-child(2) {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }

  .ourStory_depthLayer__ND5bA:nth-child(3) {
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }
}

@media (max-width: 768px) {
  .ourStory_heroTitle__fJNw2 {
    font-size: 2.5rem;
  }

  .ourStory_heroSubtitle__YzphA {
    font-size: 1rem;
  }

  .ourStory_heroTextContainer__IYr38 {
    margin-left: 0;
    align-items: center;
    text-align: center;
  }

  .ourStory_heroContent__Ht0fH {
    padding: 2rem 1rem;
  }

  .ourStory_timelineBackground__s69bf {
    padding: 0 1rem;
  }

  .ourStory_timelineTitle__Z_R4X {
    font-size: 2rem;
  }

  .ourStory_yearNavigation__MEsUq {
    gap: 1rem;
    overflow-x: auto;
    padding-bottom: 1rem;
    justify-content: flex-start;
  }

  .ourStory_yearButton__8U6Rp {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    flex-shrink: 0;
  }

  .ourStory_timelineContent__HgA6Z {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .ourStory_timelineImage__SDTuu {
    height: 300px;
  }

  .ourStory_timelineYear__fKxSh {
    font-size: 2.5rem;
  }

  .ourStory_timelineItemTitle__t1Fd6 {
    font-size: 1.8rem;
  }

  .ourStory_timelineDescription__VLFcW {
    font-size: 1rem;
  }

  .ourStory_timelineNavigation__0iu60 {
    position: static;
    justify-content: center;
    margin-top: 2rem;
  }

  .ourStory_visionSection__epapt {
    padding: 4rem 0;
  }

  .ourStory_visionContainer__DAzal {
    gap: 4rem;
    padding: 0;
  }

  .ourStory_blogItem__43b0M,
  .ourStory_blogItemReverse__APws5 {
    grid-template-columns: 1fr;
    gap: 2rem;
    min-height: auto;
    text-align: center;
    padding: 3rem 2rem;
  }

  .ourStory_blogImageContainer__BsLT9 {
    height: 300px;
    order: 1;
  }

  .ourStory_blogContent__xmjea {
    padding: 1rem;
    order: 2;
  }

  .ourStory_blogTitle__hDIux {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  .ourStory_blogText__cbTWn {
    font-size: 1rem;
    text-align: left;
  }

  .ourStory_quoteSection__rzXee {
    padding: 4rem 0;
  }

  .ourStory_quoteContainer__gTRKL {
    grid-template-columns: 1fr;
    gap: 3rem;
    padding: 0 2rem;
    text-align: center;
  }

  .ourStory_quoteTitle__Zx8la {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
  }

  .ourStory_quote__Bobyw {
    font-size: 1.1rem;
    text-align: center;
  }

  .ourStory_quoteText__pOHk4 {
    text-align: left;
  }

  /* Disable parallax on tablets */
  .ourStory_parallaxContainer__Byhmg {
    background-attachment: scroll;
  }

  /* Reduce 3D effects on tablets */
  .ourStory_bannerContainer__l3GlS {
    perspective: 700px;
  }

  .ourStory_depthLayer__ND5bA {
    border-radius: 10px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25);
  }
}

@media (max-width: 480px) {
  .ourStory_heroTitle__fJNw2 {
    font-size: 2rem;
  }

  .ourStory_heroSubtitle__YzphA {
    font-size: 0.9rem;
  }

  .ourStory_timelineTitle__Z_R4X {
    font-size: 1.5rem;
  }

  .ourStory_yearButton__8U6Rp {
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
  }

  .ourStory_timelineYear__fKxSh {
    font-size: 2rem;
  }

  .ourStory_timelineItemTitle__t1Fd6 {
    font-size: 1.5rem;
  }

  .ourStory_timelineDescription__VLFcW {
    font-size: 0.9rem;
  }

  .ourStory_timelineImage__SDTuu {
    height: 250px;
  }

  .ourStory_navButton__gBL9G {
    width: 40px;
    height: 40px;
  }

  .ourStory_visionSection__epapt {
    padding: 3rem 0;
  }

  .ourStory_visionContainer__DAzal {
    gap: 3rem;
    padding: 0;
  }

  .ourStory_blogItem__43b0M,
  .ourStory_blogItemReverse__APws5 {
    padding: 2rem 1rem;
  }

  .ourStory_blogTitle__hDIux {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .ourStory_blogText__cbTWn {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .ourStory_blogImageContainer__BsLT9 {
    height: 250px;
  }

  .ourStory_quoteSection__rzXee {
    padding: 3rem 0;
  }

  .ourStory_quoteContainer__gTRKL {
    gap: 2rem;
    padding: 0 1rem;
  }

  .ourStory_quoteTitle__Zx8la {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .ourStory_quote__Bobyw {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .ourStory_quoteText__pOHk4 {
    font-size: 0.9rem;
  }

  /* Disable parallax on mobile */
  .ourStory_parallaxContainer__Byhmg {
    background-attachment: scroll;
  }

  /* Minimal 3D effects on mobile for performance */
  .ourStory_bannerContainer__l3GlS {
    perspective: 300px;
  }

  .ourStory_depthLayer__ND5bA {
    border-radius: 6px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }

  .ourStory_depthLayer__ND5bA:nth-child(1) {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  }

  .ourStory_depthLayer__ND5bA:nth-child(2) {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  .ourStory_depthLayer__ND5bA:nth-child(3) {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  }
}

