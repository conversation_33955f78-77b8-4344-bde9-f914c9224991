/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-300: oklch(80.8% 0.114 19.571);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-red-900: oklch(39.6% 0.141 25.723);
    --color-orange-50: oklch(98% 0.016 73.684);
    --color-orange-600: oklch(64.6% 0.222 41.116);
    --color-amber-50: oklch(98.7% 0.022 95.277);
    --color-amber-100: oklch(96.2% 0.059 95.617);
    --color-amber-200: oklch(92.4% 0.12 95.746);
    --color-amber-300: oklch(87.9% 0.169 91.605);
    --color-amber-400: oklch(82.8% 0.189 84.429);
    --color-amber-500: oklch(76.9% 0.188 70.08);
    --color-amber-600: oklch(66.6% 0.179 58.318);
    --color-amber-700: oklch(55.5% 0.163 48.998);
    --color-amber-800: oklch(47.3% 0.137 46.201);
    --color-amber-900: oklch(41.4% 0.112 45.904);
    --color-yellow-100: oklch(97.3% 0.071 103.193);
    --color-yellow-200: oklch(94.5% 0.129 101.54);
    --color-yellow-800: oklch(47.6% 0.114 61.907);
    --color-green-50: oklch(98.2% 0.018 155.826);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-green-900: oklch(39.3% 0.095 152.535);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-300: oklch(80.9% 0.105 251.813);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-purple-50: oklch(97.7% 0.014 308.299);
    --color-purple-100: oklch(94.6% 0.033 307.174);
    --color-purple-600: oklch(55.8% 0.288 302.321);
    --color-purple-800: oklch(43.8% 0.218 303.724);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wider: 0.05em;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .top-20 {
    top: calc(var(--spacing) * 20);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .col-span-2 {
    grid-column: span 2 / span 2;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-auto {
    margin-inline: auto;
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-40 {
    max-height: calc(var(--spacing) * 40);
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-3xl {
    max-width: var(--container-3xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-full {
    min-width: 100%;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-amber-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-amber-200);
    }
  }
  .divide-black {
    :where(& > :not(:last-child)) {
      border-color: var(--color-black);
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .rounded-l-md {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }
  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }
  .rounded-r-md {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-r-2 {
    border-right-style: var(--tw-border-style);
    border-right-width: 2px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-b-4 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 4px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-amber-200 {
    border-color: var(--color-amber-200);
  }
  .border-amber-300 {
    border-color: var(--color-amber-300);
  }
  .border-amber-600 {
    border-color: var(--color-amber-600);
  }
  .border-amber-900 {
    border-color: var(--color-amber-900);
  }
  .border-black {
    border-color: var(--color-black);
  }
  .border-blue-300 {
    border-color: var(--color-blue-300);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-green-200 {
    border-color: var(--color-green-200);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-300 {
    border-color: var(--color-red-300);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }
  .bg-amber-50 {
    background-color: var(--color-amber-50);
  }
  .bg-amber-100 {
    background-color: var(--color-amber-100);
  }
  .bg-amber-900 {
    background-color: var(--color-amber-900);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }
  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }
  .object-cover {
    object-fit: cover;
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-12 {
    padding: calc(var(--spacing) * 12);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre-line {
    white-space: pre-line;
  }
  .text-amber-600 {
    color: var(--color-amber-600);
  }
  .text-amber-700 {
    color: var(--color-amber-700);
  }
  .text-amber-800 {
    color: var(--color-amber-800);
  }
  .text-amber-900 {
    color: var(--color-amber-900);
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-orange-600 {
    color: var(--color-orange-600);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-purple-800 {
    color: var(--color-purple-800);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-800 {
    color: var(--color-yellow-800);
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .placeholder-amber-400 {
    &::placeholder {
      color: var(--color-amber-400);
    }
  }
  .placeholder-black {
    &::placeholder {
      color: var(--color-black);
    }
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .file\:mr-4 {
    &::file-selector-button {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .file\:rounded-md {
    &::file-selector-button {
      border-radius: var(--radius-md);
    }
  }
  .file\:border-0 {
    &::file-selector-button {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .file\:bg-amber-50 {
    &::file-selector-button {
      background-color: var(--color-amber-50);
    }
  }
  .file\:px-4 {
    &::file-selector-button {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .file\:py-2 {
    &::file-selector-button {
      padding-block: calc(var(--spacing) * 2);
    }
  }
  .file\:text-sm {
    &::file-selector-button {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .file\:font-medium {
    &::file-selector-button {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }
  .file\:text-amber-700 {
    &::file-selector-button {
      color: var(--color-amber-700);
    }
  }
  .hover\:scale-105 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:border-amber-400 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-amber-400);
      }
    }
  }
  .hover\:bg-amber-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-amber-50);
      }
    }
  }
  .hover\:bg-amber-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-amber-200);
      }
    }
  }
  .hover\:bg-amber-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-amber-800);
      }
    }
  }
  .hover\:bg-black {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-black);
      }
    }
  }
  .hover\:bg-blue-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-700);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-red-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-50);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:text-amber-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-amber-800);
      }
    }
  }
  .hover\:text-amber-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-amber-900);
      }
    }
  }
  .hover\:text-black {
    &:hover {
      @media (hover: hover) {
        color: var(--color-black);
      }
    }
  }
  .hover\:text-blue-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-900);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-green-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-800);
      }
    }
  }
  .hover\:text-green-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-green-900);
      }
    }
  }
  .hover\:text-red-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-800);
      }
    }
  }
  .hover\:text-red-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-900);
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:file\:bg-amber-100 {
    &:hover {
      @media (hover: hover) {
        &::file-selector-button {
          background-color: var(--color-amber-100);
        }
      }
    }
  }
  .focus\:border-amber-500 {
    &:focus {
      border-color: var(--color-amber-500);
    }
  }
  .focus\:border-black {
    &:focus {
      border-color: var(--color-black);
    }
  }
  .focus\:border-transparent {
    &:focus {
      border-color: transparent;
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-amber-500 {
    &:focus {
      --tw-ring-color: var(--color-amber-500);
    }
  }
  .focus\:ring-black {
    &:focus {
      --tw-ring-color: var(--color-black);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:static {
    @media (width >= 64rem) {
      position: static;
    }
  }
  .lg\:inset-0 {
    @media (width >= 64rem) {
      inset: calc(var(--spacing) * 0);
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:translate-x-0 {
    @media (width >= 64rem) {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
}
:root {
  --background: #ffffff;
  --foreground: #1f2937;
}
@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #1f2937;
  }
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
.swiper-pagination-bullet,
.swiper-pagination-bullet-active {
  background: #fff !important;
}
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right {
  background-image: none !important;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/shared/Footer/footer.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/* Footer Styles */
.footer_footer__UGvN0 {
  /* background: #1e40af; */
  background: linear-gradient(135deg, #1e40af, #1c1651ff, #1d1d30ff);  overflow: hidden;
  color: #ffffff;
  padding: 4rem 0 2rem;
  position: relative;
}

.footer_container__x9CeZ {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Content Grid */
.footer_content__3aG4z {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
  padding-bottom: 3rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Brand Section */
.footer_brand__ffOQv {
  max-width: 400px;
}

.footer_brandName__m4Txr {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #white;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.footer_brandDescription__ZQmxR {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  font-weight: 400;
}

/* Social Links */
.footer_socialLinks__EvEri {
  display: flex;
  gap: 1rem;
}

.footer_socialLink__yIRWY {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #white;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer_socialLink__yIRWY:hover {
  background: #white;
  color: #4a3728;
  transform: translateY(-2px);
}

/* Link Groups */
.footer_linkGroup__xbYXp {
  display: flex;
  flex-direction: column;
}

.footer_linkGroupTitle__VjPGq {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: #white;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
}

.footer_linkList__1JHqS {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer_link__39sDb {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 400;
  line-height: 1.4;
}

.footer_link__39sDb:hover {
  color: #white;
  transform: translateX(4px);
}

/* Contact Info */
.footer_contactInfo__6zB7H {
  grid-column: 1 / -1;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer_contactTitle__btJnn {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: #white;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
}

.footer_contactDetails__Hjp7k {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.footer_contactItem__TyXHd {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.footer_contactLabel__NMVq1 {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  font-weight: 600;
  color: #white;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.footer_contactValue__D85h3 {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  font-weight: 400;
}

/* Bottom Section */
.footer_bottom__4T5SF {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer_copyright__CZ_QD {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.footer_legalLinks__ReZIE {
  display: flex;
  gap: 2rem;
}

.footer_legalLink___uEbK {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 400;
}

.footer_legalLink___uEbK:hover {
  color: #white;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer_footer__UGvN0 {
    padding: 3rem 0 1.5rem;
  }

  .footer_container__x9CeZ {
    padding: 0 1.5rem;
  }

  .footer_content__3aG4z {
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
  }

  .footer_brand__ffOQv {
    grid-column: 1 / -1;
    max-width: none;
    text-align: center;
    margin-bottom: 1rem;
  }

  .footer_socialLinks__EvEri {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .footer_footer__UGvN0 {
    padding: 2.5rem 0 1rem;
  }

  .footer_container__x9CeZ {
    padding: 0 1rem;
  }

  .footer_content__3aG4z {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .footer_brand__ffOQv {
    margin-bottom: 1.5rem;
  }

  .footer_brandName__m4Txr {
    font-size: 1.75rem;
  }

  .footer_brandDescription__ZQmxR {
    font-size: 0.9rem;
  }

  .footer_linkGroup__xbYXp {
    align-items: center;
  }

  .footer_linkGroupTitle__VjPGq {
    font-size: 1.125rem;
    margin-bottom: 1rem;
  }

  .footer_contactInfo__6zB7H {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    text-align: center;
  }

  .footer_contactDetails__Hjp7k {
    grid-template-columns: 1fr;
    gap: 1rem;
    text-align: center;
  }

  .footer_bottom__4T5SF {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer_legalLinks__ReZIE {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .footer_footer__UGvN0 {
    padding: 2rem 0 1rem;
  }

  .footer_brandName__m4Txr {
    font-size: 1.5rem;
  }

  .footer_brandDescription__ZQmxR {
    font-size: 0.85rem;
  }

  .footer_socialLinks__EvEri {
    gap: 0.75rem;
  }

  .footer_socialLink__yIRWY {
    width: 36px;
    height: 36px;
  }

  .footer_linkGroupTitle__VjPGq {
    font-size: 1rem;
  }

  .footer_link__39sDb {
    font-size: 0.85rem;
  }

  .footer_contactTitle__btJnn {
    font-size: 1rem;
  }

  .footer_contactLabel__NMVq1 {
    font-size: 0.8rem;
  }

  .footer_contactValue__D85h3 {
    font-size: 0.85rem;
  }

  .footer_legalLinks__ReZIE {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/wholesale/WholesaleUserMenu.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
.WholesaleUserMenu_wholesaleUserMenu__JZgPZ {
  position: relative;
  display: inline-block;
}

.WholesaleUserMenu_userButton__Ee0T_ {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
}

.WholesaleUserMenu_userButton__Ee0T_:hover {
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
  transform: translateY(-1px);
}

.WholesaleUserMenu_userInfo__R496o {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.WholesaleUserMenu_wholesaleLabel__IJ6Qz {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
  line-height: 1;
}

.WholesaleUserMenu_userName__B2ckG {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.2;
  margin-top: 0.125rem;
}

.WholesaleUserMenu_chevron__RYj32 {
  transition: transform 0.2s ease;
  opacity: 0.8;
}

.WholesaleUserMenu_chevron__RYj32.WholesaleUserMenu_open__wagO_ {
  transform: rotate(180deg);
}

.WholesaleUserMenu_dropdown__I8_I5 {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  z-index: 1000;
}

.WholesaleUserMenu_dropdownContent__AWqeQ {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  min-width: 280px;
  overflow: hidden;
  animation: WholesaleUserMenu_dropdownFadeIn__BWQMT 0.2s ease-out;
}

@keyframes WholesaleUserMenu_dropdownFadeIn__BWQMT {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.WholesaleUserMenu_userDetails__aoarO {
  padding: 1.5rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

.WholesaleUserMenu_userEmail__sbM_5 {
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.WholesaleUserMenu_statusBadge__YPSLj {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #059669;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.WholesaleUserMenu_statusDot__fpoWC {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: WholesaleUserMenu_pulse__XRXnZ 2s infinite;
}

@keyframes WholesaleUserMenu_pulse__XRXnZ {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.WholesaleUserMenu_divider__wYcRN {
  height: 1px;
  background: #e5e7eb;
}

.WholesaleUserMenu_logoutButton__C_oiB {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.WholesaleUserMenu_logoutButton__C_oiB:hover {
  background: #f9fafb;
  color: #ef4444;
}

.WholesaleUserMenu_logoutButton__C_oiB svg {
  transition: transform 0.2s ease;
}

.WholesaleUserMenu_logoutButton__C_oiB:hover svg {
  transform: translateX(2px);
}

.WholesaleUserMenu_overlay__zI0Xr {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
  .WholesaleUserMenu_userButton__Ee0T_ {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
  
  .WholesaleUserMenu_wholesaleLabel__IJ6Qz {
    font-size: 0.6875rem;
  }
  
  .WholesaleUserMenu_userName__B2ckG {
    font-size: 0.8125rem;
  }
  
  .WholesaleUserMenu_dropdownContent__AWqeQ {
    min-width: 260px;
    margin-right: 1rem;
  }
  
  .WholesaleUserMenu_userDetails__aoarO {
    padding: 1.25rem;
  }
  
  .WholesaleUserMenu_logoutButton__C_oiB {
    padding: 0.875rem 1.25rem;
  }
}

@media (max-width: 480px) {
  .WholesaleUserMenu_userInfo__R496o {
    display: none;
  }
  
  .WholesaleUserMenu_userButton__Ee0T_ {
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    justify-content: center;
  }
  
  .WholesaleUserMenu_userButton__Ee0T_::before {
    content: 'W';
    font-weight: 700;
    font-size: 1rem;
  }
  
  .WholesaleUserMenu_chevron__RYj32 {
    display: none;
  }
  
  .WholesaleUserMenu_dropdown__I8_I5 {
    right: -1rem;
  }
  
  .WholesaleUserMenu_dropdownContent__AWqeQ {
    min-width: 240px;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/shared/Header/header.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/* Blue and White Theme Variables */
.header_Headerroot__qFKmr {
  --cast-stone-blue: #2563eb;
  --cast-stone-light-blue: #3b82f6;
  --cast-stone-blue-50: #eff6ff;
  --cast-stone-white: #ffffff;
  --cast-stone-dark-text: #1f2937;
  --cast-stone-gray-text: #4b5563;
  --cast-stone-shadow: rgba(37, 99, 235, 0.1);
  --cast-stone-shadow-hover: rgba(37, 99, 235, 0.15);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Header Styles - Completely transparent design */
.header_header__Zkve9 {
  background: transparent;
  backdrop-filter: none;
  border: none;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

/* Header when scrolled - stays transparent */
.header_header__Zkve9.header_scrolled__KaDQf {
  /* background: transparent;
  backdrop-filter: none; */
    transform: translateY(-100%);

}

/* Header for non-home pages - blue background with white text */
.header_header__Zkve9.header_nonHomePage__nsuQe {
  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));
  backdrop-filter: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(37, 99, 235, 0.1);
}

.header_header__Zkve9.header_nonHomePage__nsuQe.header_scrolled__KaDQf {
  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));
  box-shadow: 0 4px 20px rgba(37, 99, 235, 0.15);
}

.header_container__fzi45 {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

/* Logo Styles */
.header_logo__Dghye {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.header_logoLink__vkk8O {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.header_scrolled__KaDQf .header_logoLink__vkk8O {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.header_nonHomePage__nsuQe .header_logoLink__vkk8O {
  color: var(--cast-stone-white);
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.header_logoLink__vkk8O:hover {
  transform: translateY(-1px);
}

.header_logoText__lEG6_ {
  font-size: 1.8rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  line-height: 1;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  text-transform: uppercase;
}

.header_logoSubtext__6NOcI {
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
}

.header_nonHomePage__nsuQe .header_logoSubtext__6NOcI {
  color: rgba(0, 0, 0, 0.9);
}

/* Navigation Styles */
.header_nav__NxBck {
  display: flex;
  align-items: center;
}

.header_navList__3vdeA {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0;
  align-items: center;
}

.header_navItem__83azt {
  position: relative;
  display: flex;
  align-items: center;
}

.header_navLink__r8nlj,
.header_navButton__nsuVv {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.9rem;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.header_scrolled__KaDQf .header_navLink__r8nlj,
.header_scrolled__KaDQf .header_navButton__nsuVv {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.header_nonHomePage__nsuQe .header_navLink__r8nlj,
.header_nonHomePage__nsuQe .header_navButton__nsuVv {
  color: var(--cast-stone-white);
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.header_navLink__r8nlj:hover,
.header_navButton__nsuVv:hover {
  color: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
}

.header_scrolled__KaDQf .header_navLink__r8nlj:hover,
.header_scrolled__KaDQf .header_navButton__nsuVv:hover {
  color: rgba(255, 255, 255, 1);
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
}

.header_nonHomePage__nsuQe .header_navLink__r8nlj:hover,
.header_nonHomePage__nsuQe .header_navButton__nsuVv:hover {
  color: rgba(255, 255, 255, 1);
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.4);
}

.header_navLink__r8nlj::after,
.header_navButton__nsuVv::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.header_scrolled__KaDQf .header_navLink__r8nlj::after,
.header_scrolled__KaDQf .header_navButton__nsuVv::after {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.header_nonHomePage__nsuQe .header_navLink__r8nlj::after,
.header_nonHomePage__nsuQe .header_navButton__nsuVv::after {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.header_navLink__r8nlj:hover::after,
.header_navButton__nsuVv:hover::after,
.header_navButton__nsuVv.header_active__PVd5K::after {
  width: 80%;
}

/* Dropdown Styles */
.header_dropdownContainer__VWoxc {
  position: relative;
  display: flex;
  align-items: center;
}

.header_dropdownIcon__Zm6Wf {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-smooth);
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.header_dropdownIcon__Zm6Wf.header_rotated__xpZ_L {
  transform: rotate(180deg);
  color: rgba(255, 255, 255, 0.9);
}

.header_nonHomePage__nsuQe .header_dropdownIcon__Zm6Wf {
  color: rgba(255, 255, 255, 0.8);
}

.header_nonHomePage__nsuQe .header_dropdownIcon__Zm6Wf.header_rotated__xpZ_L {
  color: var(--cast-stone-white);
}

.header_loadingIcon__Ak61V {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.header_dropdown__zM7fw {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--cast-stone-white);
  border: 1px solid rgba(74, 55, 40, 0.1);
  border-radius: 8px;
  box-shadow: 0 8px 32px var(--cast-stone-shadow-hover);
  min-width: 280px;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-10px);
  transition: var(--transition-smooth);
  animation: header_dropdownSlideIn__5QwVb 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  backdrop-filter: blur(10px);
}

@keyframes header_dropdownSlideIn__5QwVb {
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
  }
}

.header_dropdownContent__n7Hlt {
  position: relative;
  background: var(--cast-stone-white);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(37, 99, 235, 0.12);
  min-width: 280px;
  z-index: 1001;
  border: 1px solid rgba(37, 99, 235, 0.08);
  padding: 0.5rem 0;
}

.header_dropdownOpen__uAaN6 {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
}

.header_dropdownOpen__uAaN6 .header_dropdownContent__n7Hlt {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.header_dropdownList__woAOM {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.header_dropdownItem__QAhhq {
  position: relative;
}

.header_dropdownLink___cGcQ {
  display: block;
  padding: 0.75rem 1.25rem;
  color: var(--cast-stone-dark-text);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
}

.header_dropdownLink___cGcQ:hover {
  background: rgba(37, 99, 235, 0.04);
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-blue);
  transform: translateX(2px);
}

.header_dropdownArrow__OOejK {
  float: right;
  font-size: 0.7rem;
  color: var(--cast-stone-gray-text);
  transition: var(--transition-fast);
}

.header_dropdownLink___cGcQ:hover .header_dropdownArrow__OOejK {
  color: var(--cast-stone-blue);
}

/* Sub-dropdown Styles */
.header_subDropdownList__3jCpc {
  list-style: none;
  margin: 0;
  padding: 0;
  background: rgba(37, 99, 235, 0.02);
  border-top: 1px solid rgba(37, 99, 235, 0.08);
}

.header_subDropdownItem__RZ7SQ {
  position: relative;
}

.header_subDropdownLink__XLqny {
  display: block;
  padding: 0.6rem 1.25rem 0.6rem 2rem;
  color: var(--cast-stone-gray-text);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
  position: relative;
}

.header_subDropdownLink__XLqny::before {
  content: '→  ';
  position: absolute;
  left: 0.75rem;
  color: var(--cast-stone-gray-text);
  font-size: 0.7rem;
  transition: var(--transition-fast);
}

.header_subDropdownLink__XLqny:hover {
  background: rgba(37, 99, 235, 0.06);
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-light-blue);
  transform: translateX(2px);
}

.header_subDropdownLink__XLqny:hover::before {
  color: var(--cast-stone-blue);
  transform: translateX(2px);
}

/* Sub-sub-dropdown Styles (Level 3) */
.header_subSubDropdownList__zA6dK {
  list-style: none;
  margin: 0;
  padding: 0;
  background: rgba(37, 99, 235, 0.04);
  border-top: 1px solid rgba(37, 99, 235, 0.12);
}

.header_subSubDropdownItem__iPXjR {
  position: relative;
}

.header_subSubDropdownLink__zfl1R {
  display: block;
  padding: 0.5rem 1.25rem 0.5rem 2.5rem;
  color: var(--cast-stone-gray-text);
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 6px solid transparent;
  position: relative;
}

.header_subSubDropdownLink__zfl1R::before {
  content: '⤷  ';
  position: absolute;
  left: 1.5rem;
  color: var(--cast-stone-gray-text);
  font-size: 0.65rem;
  transition: var(--transition-fast);
}

.header_subSubDropdownLink__zfl1R:hover {
  background: rgba(37, 99, 235, 0.08);
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-light-blue);
  transform: translateX(2px);
}

.header_subSubDropdownLink__zfl1R:hover::before {
  color: var(--cast-stone-blue);
  transform: translateX(2px);
}

/* Hierarchical Dropdown Styles */
.header_hierarchicalDropdownItem___5qX9 {
  position: relative;
}

.header_submenu__YVOyR {
  position: absolute;
  left: 100%;
  top: 0;
  background: var(--cast-stone-white);
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
  min-width: 220px;
  width: max-content;
  z-index: 1002;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-10px);
  transition: var(--transition-smooth);
  margin-left: 4px;
  border: 1px solid rgba(37, 99, 235, 0.08);
  padding: 0.5rem 0;
}

.header_hierarchicalDropdownItem___5qX9:hover .header_submenu__YVOyR {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.header_submenuItem__QVr8s {
  position: relative;
  border-bottom: 1px solid rgba(37, 99, 235, 0.05);
}

.header_submenuItem__QVr8s:last-child {
  border-bottom: none;
}

.header_submenuLink__oFSP2 {
  display: block;
  padding: 0.75rem 1.25rem;
  color: var(--cast-stone-dark-text);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
  position: relative;
}

.header_submenuLink__oFSP2:hover {
  background: rgba(37, 99, 235, 0.04);
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-blue);
  transform: translateX(2px);
}

.header_submenuLink__oFSP2 .header_dropdownArrow__OOejK {
  float: right;
  font-size: 0.7rem;
  color: var(--cast-stone-gray-text);
  transition: var(--transition-fast);
}

.header_submenuLink__oFSP2:hover .header_dropdownArrow__OOejK {
  color: var(--cast-stone-blue);
}

.header_subSubmenu__J3RyA {
  position: absolute;
  left: 100%;
  top: 0;
  background: var(--cast-stone-white);
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
  min-width: 200px;
  width: max-content;
  z-index: 1003;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-10px);
  transition: var(--transition-smooth);
  margin-left: 4px;
  border: 1px solid rgba(37, 99, 235, 0.08);
  padding: 0.5rem 0;
}

.header_submenuItem__QVr8s:hover .header_subSubmenu__J3RyA {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.header_subSubmenuLink__eZlvv {
  display: block;
  padding: 0.6rem 1.25rem;
  color: var(--cast-stone-gray-text);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
  border-bottom: 1px solid rgba(37, 99, 235, 0.05);
}

.header_subSubmenuLink__eZlvv:last-child {
  border-bottom: none;
}

.header_subSubmenuLink__eZlvv:hover {
  background: rgba(37, 99, 235, 0.06);
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-light-blue);
  transform: translateX(2px);
}

@keyframes header_submenuSlideIn__cbWDj {
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
  }
}

/* Cart Styles */
.header_cartContainer__nGodr {
  display: flex;
  align-items: center;
  margin-left: 0.25rem;
}

.header_cartLink__ymTVi {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: 8px;
  transition: var(--transition-smooth);
  position: relative;
}

.header_nonHomePage__nsuQe .header_cartLink__ymTVi {
  color: var(--cast-stone-white);
}

.header_cartLink__ymTVi:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.header_nonHomePage__nsuQe .header_cartLink__ymTVi:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--cast-stone-white);
}

.header_cartIconWrapper__vnRO_ {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header_cartBadge__F45rS {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc2626;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: header_cartBadgeAppear__BXBeQ 0.3s ease-out;
}

@keyframes header_cartBadgeAppear__BXBeQ {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Mobile Menu Button */
.header_mobileMenuButton__lZRXB {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  margin-left: 1px;
  border-radius: 4px;
  transition: var(--transition-smooth);
}

.header_mobileMenuButton__lZRXB:hover {
  /* background: rgba(255, 255, 255, 0.1); */
  background: transparent;
}

.header_nonHomePage__nsuQe .header_mobileMenuButton__lZRXB:hover {
  /* background: rgba(255, 255, 255, 0.15); */
    background: transparent;

}

/* Hamburger Icon */
.header_hamburgerIcon__Npbnf {
  width: 20px;
  height: 18px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.header_hamburgerIcon__Npbnf span {
  display: block;
  height: 2px;
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1px;
  transition: var(--transition-smooth);
  /* transform-origin: center; */
}

.header_nonHomePage__nsuQe .header_hamburgerIcon__Npbnf span {
  background: #000000;
}

/* Special case for retail locator page with dark background - higher specificity */
.header_nonHomePage__nsuQe.header_retailLocatorPage__56uks .header_hamburgerIcon__Npbnf span,
.header_retailLocatorPage__56uks .header_hamburgerIcon__Npbnf span {
  background: rgba(9, 9, 9, 0.9) !important;
}

/* Hamburger Animation */
.header_hamburgerIcon__Npbnf.header_open__q8G1w span:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

.header_hamburgerIcon__Npbnf.header_open__q8G1w span:nth-child(2) {
  opacity: 0;
  transform: scaleX(0);
}

.header_hamburgerIcon__Npbnf.header_open__q8G1w span:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

/* Mobile Menu Overlay */
.header_mobileMenuOverlay__by_2H {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: rgba(0, 0, 0, 0.95); */
    background: transparent;
  backdrop-filter: blur(10px);
  z-index: 999;
  animation: header_mobileMenuFadeIn__n7Sep 0.3s ease-out;
}

/* Mobile Menu Overlay for non-home pages */
.header_nonHomePage__nsuQe .header_mobileMenuOverlay__by_2H {
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
}

/* Special case for retail locator page */
.header_retailLocatorPage__56uks .header_mobileMenuOverlay__by_2H {
  background: transparent;
  backdrop-filter: blur(10px);
}

.header_mobileMenuContent__kct6e {
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header_mobileNav__QdVDa {
  flex: 1;
}

.header_mobileNavList__81xgK {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.header_mobileNavItem__G4Sap {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header_mobileNavItem__G4Sap:last-child {
  border-bottom: none;
}

.header_mobileNavLink__0ieic {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 1.25rem;
  font-weight: 500;
  padding: 1.5rem 0;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.header_mobileNavLink__0ieic:hover {
  color: rgba(255, 255, 255, 1);
  transform: translateX(8px);
}

/* Mobile Navigation Buttons (for dropdowns) */
.header_mobileNavButton__P6o1A {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 1.25rem;
  font-weight: 500;
  padding: 1.5rem 0;
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  font-family: inherit;
}

.header_mobileNavButton__P6o1A:hover {
  color: rgba(255, 255, 255, 1);
  transform: translateX(8px);
}

.header_mobileNavButton__P6o1A.header_active__PVd5K {
  color: rgba(255, 255, 255, 1);
}

/* Mobile Dropdown Icon */
.header_mobileDropdownIcon__TrdnB {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-smooth);
  margin-left: 8px;
}

.header_mobileDropdownIcon__TrdnB.header_rotated__xpZ_L {
  transform: rotate(180deg);
}

.header_mobileDropdownIcon__TrdnB svg {
  width: 12px;
  height: 8px;
}

/* Mobile Sub Menu */
.header_mobileSubMenu__Kyx_D {
  list-style: none;
  margin: 0;
  padding: 0;
  padding-left: 1rem;
  border-left: 2px solid rgba(255, 255, 255, 0.2);
  margin-left: 1rem;
  animation: header_mobileSubMenuSlideIn__zZ5vP 0.3s ease-out;
}

.header_mobileSubMenuItem__lOAAo {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.header_mobileSubMenuItem__lOAAo:last-child {
  border-bottom: none;
}

.header_mobileSubMenuLink__HG9La {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 400;
  padding: 1rem 0;
  transition: var(--transition-smooth);
  text-transform: none;
  letter-spacing: normal;
}

.header_mobileSubMenuLink__HG9La:hover {
  color: rgba(255, 255, 255, 0.9);
  transform: translateX(4px);
}

@keyframes header_mobileSubMenuSlideIn__zZ5vP {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes header_mobileMenuFadeIn__n7Sep {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header_container__fzi45 {
    padding: 0 1.5rem;
  }

  .header_navList__3vdeA {
    gap: 0;
  }

  .header_navLink__r8nlj,
  .header_navButton__nsuVv {
    padding: 1.5rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .header_container__fzi45 {
    padding: 0 1rem;
    height: 70px;
  }

  .header_logoText__lEG6_ {
    font-size: 1.75rem;
  }

  .header_logoSubtext__6NOcI {
    font-size: 0.7rem;
  }

  .header_navList__3vdeA {
    gap: 0;
  }

  .header_navLink__r8nlj,
  .header_navButton__nsuVv {
    padding: 1.25rem 0.75rem;
    font-size: 0.85rem;
  }

  .header_dropdown__zM7fw {
    min-width: 200px;
  }
}

@media (max-width: 640px) {
  .header_nav__NxBck {
    display: none; /* Hidden on mobile, replaced by hamburger menu */
  }

  .header_container__fzi45 {
    justify-content: space-between;
  }

  .header_mobileMenuButton__lZRXB {
    display: block;
  }
}

