.wholesaleUserMenu {
  position: relative;
  display: inline-block;
}

.userButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
}

.userButton:hover {
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
  transform: translateY(-1px);
}

.userInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.wholesaleLabel {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
  line-height: 1;
}

.userName {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.2;
  margin-top: 0.125rem;
}

.chevron {
  transition: transform 0.2s ease;
  opacity: 0.8;
}

.chevron.open {
  transform: rotate(180deg);
}

.dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  z-index: 1000;
}

.dropdownContent {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  min-width: 280px;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.userDetails {
  padding: 1.5rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

.userEmail {
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.statusBadge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #059669;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusDot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.divider {
  height: 1px;
  background: #e5e7eb;
}

.logoutButton {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.logoutButton:hover {
  background: #f9fafb;
  color: #ef4444;
}

.logoutButton svg {
  transition: transform 0.2s ease;
}

.logoutButton:hover svg {
  transform: translateX(2px);
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
  .userButton {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
  
  .wholesaleLabel {
    font-size: 0.6875rem;
  }
  
  .userName {
    font-size: 0.8125rem;
  }
  
  .dropdownContent {
    min-width: 260px;
    margin-right: 1rem;
  }
  
  .userDetails {
    padding: 1.25rem;
  }
  
  .logoutButton {
    padding: 0.875rem 1.25rem;
  }
}

@media (max-width: 480px) {
  .userInfo {
    display: none;
  }
  
  .userButton {
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    justify-content: center;
  }
  
  .userButton::before {
    content: 'W';
    font-weight: 700;
    font-size: 1rem;
  }
  
  .chevron {
    display: none;
  }
  
  .dropdown {
    right: -1rem;
  }
  
  .dropdownContent {
    min-width: 240px;
  }
}
