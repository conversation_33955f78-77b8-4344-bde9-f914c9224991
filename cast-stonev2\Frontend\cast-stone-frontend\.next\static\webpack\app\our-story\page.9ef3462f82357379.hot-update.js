"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/our-story/page",{

/***/ "(app-pages-browser)/./src/app/our-story/page.tsx":
/*!************************************!*\
  !*** ./src/app/our-story/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ourStory.module.css */ \"(app-pages-browser)/./src/app/our-story/ourStory.module.css\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst OurStoryPage = ()=>{\n    _s();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Banner images array\n    const bannerImages = [\n        '/images/CollectionBackground.jpg',\n        '/images/CollectionBackground2.jpg',\n        '/images/CollectionBackground3.jpg',\n        '/images/catalog-banner-bg.jpg'\n    ];\n    // Auto-rotate banner images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OurStoryPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"OurStoryPage.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"OurStoryPage.useEffect.interval\": (prevIndex)=>(prevIndex + 1) % bannerImages.length\n                    }[\"OurStoryPage.useEffect.interval\"]);\n                }\n            }[\"OurStoryPage.useEffect.interval\"], 5000); // Change image every 5 seconds\n            return ({\n                \"OurStoryPage.useEffect\": ()=>clearInterval(interval)\n            })[\"OurStoryPage.useEffect\"];\n        }\n    }[\"OurStoryPage.useEffect\"], [\n        bannerImages.length\n    ]);\n    // Timeline data with images\n    const timelineData = [\n        {\n            year: '2010',\n            title: 'Foundation',\n            description: 'Cast Stone was founded with a vision to revolutionize architectural stone design and manufacturing. Our journey began with a commitment to excellence and innovation in every piece we create.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2012',\n            title: 'First Major Project',\n            description: 'Completed our first large-scale commercial project, establishing our reputation in the industry. This milestone project showcased our capabilities and set the foundation for future growth.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2015',\n            title: 'Innovation Breakthrough',\n            description: 'Developed proprietary casting techniques that enhanced durability and aesthetic appeal. Our research and development team achieved breakthrough innovations that set new industry standards.',\n            image: '/images/CollectionBackground3.jpg'\n        },\n        {\n            year: '2018',\n            title: 'International Expansion',\n            description: 'Expanded operations internationally, bringing our expertise to global markets. We established partnerships worldwide and began serving clients across multiple continents.',\n            image: '/images/catalog-banner-bg.jpg'\n        },\n        {\n            year: '2020',\n            title: 'Sustainable Practices',\n            description: 'Implemented eco-friendly manufacturing processes and sustainable material sourcing. Our commitment to environmental responsibility became a cornerstone of our operations.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2023',\n            title: 'Digital Innovation',\n            description: 'Launched advanced digital design tools and virtual consultation services. We embraced technology to enhance customer experience and streamline our design process.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2024',\n            title: 'Industry Leadership',\n            description: 'Recognized as industry leader with over 500 successful projects worldwide. Our dedication to quality and innovation has established us as the premier choice for architectural stone solutions.',\n            image: '/images/CollectionBackground3.jpg'\n        }\n    ];\n    // Scroll to next section\n    const scrollToTimeline = ()=>{\n        const timelineSection = document.getElementById('timeline-section');\n        if (timelineSection) {\n            timelineSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Navigate timeline\n    const navigateTimeline = (direction)=>{\n        if (direction === 'prev' && selectedYear > 0) {\n            setSelectedYear(selectedYear - 1);\n        } else if (direction === 'next' && selectedYear < timelineData.length - 1) {\n            setSelectedYear(selectedYear + 1);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().storyPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().bannerContainer),\n                    children: [\n                        bannerImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().bannerImage),\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: index === currentImageIndex ? 1 : 0,\n                                    scale: index === currentImageIndex ? 1.05 : 1\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeInOut\"\n                                },\n                                style: {\n                                    backgroundImage: \"url(\".concat(image, \")\"),\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    width: '100%',\n                                    height: '100%',\n                                    backgroundSize: 'cover',\n                                    backgroundPosition: 'center',\n                                    zIndex: index === currentImageIndex ? 1 : 0\n                                }\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroTextContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroTitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.5\n                                            },\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroSubtitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.8\n                                            },\n                                            children: \"In 2010, the world of architectural stone made the discovery of a new brand.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().scrollArrow),\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        delay: 1.2\n                                    },\n                                    onClick: scrollToTimeline,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().arrowIcon),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M7 10L12 15L17 10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().arrowText),\n                                            children: \"Explore Our Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"timeline-section\",\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineBackground),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineTitle),\n                                    children: \"TIMELINE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().yearNavigation),\n                                    children: [\n                                        timelineData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().yearButton), \" \").concat(index === selectedYear ? (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().yearButtonActive) : ''),\n                                                onClick: ()=>setSelectedYear(index),\n                                                children: item.year\n                                            }, item.year, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().moreButton),\n                                            children: [\n                                                \"More\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"16\",\n                                                    height: \"16\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M19 9L12 16L5 9\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineContentContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineContent),\n                                    initial: {\n                                        opacity: 0,\n                                        x: 50\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineImageContainer),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: timelineData[selectedYear].image,\n                                                alt: timelineData[selectedYear].title,\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineImage)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineTextContainer),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineYear),\n                                                    children: timelineData[selectedYear].year\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineItemTitle),\n                                                    children: timelineData[selectedYear].title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineDescription),\n                                                    children: timelineData[selectedYear].description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, selectedYear, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineNavigation),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().prevButton)),\n                                            onClick: ()=>navigateTimeline('prev'),\n                                            disabled: selectedYear === 0,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M15 18L9 12L15 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().nextButton)),\n                                            onClick: ()=>navigateTimeline('next'),\n                                            disabled: selectedYear === timelineData.length - 1,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 18L15 12L9 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OurStoryPage, \"yIsq3P1r70jr1ywORtZUlvMWrG4=\");\n_c = OurStoryPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OurStoryPage);\nvar _c;\n$RefreshReg$(_c, \"OurStoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/our-story/page.tsx\n"));

/***/ })

});