/* Hero Section Styles */
.hero {
  position: relative;
  height: 100vh;
  min-height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #1a1a1a; /* Fallback color */
}

/* Image Background Carousel */
.imageContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.imageSlide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1.5s ease-in-out;
  transform: scale(1);
  will-change: transform, opacity;
}

.imageSlide.active {
  opacity: 1;
  animation: smoothZoom 5s ease-out forwards;
}

.imageSlide:not(.active) {
  animation: none;
  transform: scale(1.06); /* Keep zoomed state when inactive */
}

/* Smooth zoom animation without reset */
@keyframes smoothZoom {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.06);
  }
}

.backgroundImage {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: 2;
}

/* Content Styles */
.container {
  position: relative;
  z-index: 3;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.content {
  max-width: 900px;
  margin: 0 auto;
  padding-top: 2rem;
}

.title {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.1;
  margin-bottom: 2rem;
  color: #ffffff;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.02em;
}

.titleLine1,
.titleLine2 {
  display: block;
  animation: fadeInUp 1s ease-out forwards;
}

.titleLine1 {
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateY(30px);
}

.titleLine2 {
  animation-delay: 0.6s;
  opacity: 0;
  transform: translateY(30px);
}

.subtitle {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.3rem;
  font-weight: 300;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
  animation: fadeInUp 1s ease-out 0.9s forwards;
  opacity: 0;
  transform: translateY(30px);
}

/* Button Styles */
.actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 1.2s forwards;
  opacity: 0;
  transform: translateY(30px);
}

.primaryButton,
.secondaryButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1.2rem 3rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.15em;
  text-transform: uppercase;
  text-decoration: none;
  border-radius: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  min-width: 220px;
}

.primaryButton {
  background: rgba(255, 255, 255, 0.9);
  color: rgba(0, 0, 0, 0.8);
  border-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.primaryButton:hover {
  background: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.secondaryButton {
  background: transparent;
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 1);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
}

.buttonText {
  position: relative;
  z-index: 2;
}

.buttonRipple {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: scale(0);
  transition: transform 0.6s ease-out;
  z-index: 1;
}

.primaryButton:active .buttonRipple,
.secondaryButton:active .buttonRipple {
  transform: scale(1);
}

/* Scroll Indicator */
.scrollIndicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  animation: fadeInUp 1s ease-out 1.5s forwards;
  opacity: 0;
}

.scrollArrow {
  color: #ffffff;
  animation: bounce 2s infinite;
  cursor: pointer;
  transition: color 0.3s ease;
}

.scrollArrow:hover {
  color: #d4af8c;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .title {
    font-size: 3.5rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .container {
    padding: 0 1.5rem;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .actions {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 280px;
    padding: 0.875rem 2rem;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .hero {
    min-height: 500px;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .primaryButton,
  .secondaryButton {
    font-size: 0.8rem;
    padding: 0.75rem 1.5rem;
  }
}

/* Navigation Arrows */
.navArrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  /* background: rgba(255, 255, 255, 0.1); */
  /* backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 0%; */
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 4;
  opacity: 0.7;
}

.navArrow:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
  color: rgba(255, 255, 255, 1);
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

.navArrowLeft {
  left: 2rem;
}

.navArrowRight {
  right: 2rem;
}

/* Indicator Dots */
.indicators {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 3rem;
  padding: 1rem 0;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.indicator:hover {
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.indicatorActive {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .hero {
    min-height: 500px;
  }

  .navArrow {
    width: 50px;
    height: 50px;
    opacity: 0.6;
  }

  .navArrowLeft {
    left: 1rem;
  }

  .navArrowRight {
    right: 1rem;
  }

  .indicators {
    margin-top: 2rem;
    gap: 0.5rem;
  }

  .indicator {
    width: 10px;
    height: 10px;
  }
}
