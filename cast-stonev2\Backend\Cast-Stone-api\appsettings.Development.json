{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=dpg-d1v5icmmcj7s73f392a0-a.singapore-postgres.render.com;Database=caststoneinternational;Username=caststoneinternational_user;Password=********************************;Port=5432;SSL Mode=Require;Trust Server Certificate=true;"}, "Cloudinary": {"CloudName": "dp6u85kkx", "ApiKey": "257221466619946", "ApiSecret": "JYo-xIQQeywLhircNkyLPUgjhFw"}, "SmtpSettings": {"Host": "smtp-relay.brevo.com", "Port": 587, "Username": "<EMAIL>", "Password": "q0QZ7RBJNUwHXG1D", "EnableSsl": true, "FromEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "FromName": "Cast Stone", "AdminEmail": "<EMAIL>"}, "Stripe": {"SecretKey": "your-stripe-secret-key", "PublishableKey": "your-stripe-publishable-key"}, "PayPal": {"ClientId": "your-paypal-client-id", "Secret": "your-paypal-secret", "Environment": "sandbox"}, "ApplePay": {"MerchantId": "merchant.com.example", "DomainVerificationPath": ".well-known/apple-developer-merchantid-domain-association"}}