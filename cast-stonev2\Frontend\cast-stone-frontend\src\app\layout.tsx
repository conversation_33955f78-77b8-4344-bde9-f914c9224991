import type { <PERSON>ada<PERSON> } from "next";
import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import Header from "@/components/shared/Header/Header";
import Footer from "@/components/shared/Footer/Footer";
import { CartProvider } from "@/contexts/CartContext";
import { WholesaleAuthProvider } from "@/contexts/WholesaleAuthContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Cast Stone - Modern Web Application",
  description: "A modern web application built with Next.js and .NET Core",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <WholesaleAuthProvider>
          <CartProvider>
            <Header />
            <main className="min-h-screen">
              {children}
            </main>
            <Footer />
          </CartProvider>
        </WholesaleAuthProvider>
      </body>
    </html>
  );
}
