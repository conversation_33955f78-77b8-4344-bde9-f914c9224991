/* Magazine Section - Professional & Minimalist Design */
.magazineSection {
  padding: 4rem 0;
  background: #ffffff;
  position: relative;
}

.magazineSection:nth-child(even) {
  background: #fafafa;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 500px;
}

/* Image positioning variants */
.right .content {
  grid-template-columns: 1fr 1fr;
}

.left .content {
  grid-template-columns: 1fr 1fr;
}

.left .textContent {
  order: 2;
}

.left .imageContent {
  order: 1;
}

/* Text Content */
.textContent {
  padding: 2rem 0;
  max-width: 500px;
  margin: 0 auto;
}

.badge {
  display: inline-block;
  background: #f3f4f6;
  color: #374151;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
  margin-bottom: 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.subtitle {
  font-size: 1.25rem;
  font-weight: 500;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 1.5rem;
}

.description {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.7;
  margin-bottom: 2rem;
  font-weight: 400;
}

.additionalContent {
  margin-bottom: 2rem;
}

.ctaContainer {
  margin-top: 2rem;
}

.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: #1f2937;
  color: white;
  padding: 1rem 2rem;
  border: none;
  border-radius: 0;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.ctaButton:hover {
  background: #374151;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.ctaIcon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
  transition: transform 0.3s ease;
}

.ctaButton:hover .ctaIcon {
  transform: translateX(4px);
}

/* Image Content */
.imageContent {
  position: relative;
  height: 100%;
  min-height: 400px;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.imageContainer:hover .image {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.imageContainer:hover .imageOverlay {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content {
    gap: 3rem;
  }
  
  .title {
    font-size: 2.25rem;
  }
}

@media (max-width: 768px) {
  .magazineSection {
    padding: 3rem 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .left .textContent,
  .right .textContent {
    order: 2;
  }
  
  .left .imageContent,
  .right .imageContent {
    order: 1;
  }
  
  .textContent {
    max-width: 100%;
    padding: 1rem 0;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .imageContent {
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .magazineSection {
    padding: 2rem 0;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .ctaButton {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .imageContent {
    min-height: 250px;
  }
}
