/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_wow_js_dist_wow_js"],{

/***/ "(app-pages-browser)/./node_modules/wow.js/dist/wow.js":
/*!*****************************************!*\
  !*** ./node_modules/wow.js/dist/wow.js ***!
  \*****************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;(function (global, factory) {\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [module, exports], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else { var mod; }\n})(this, function (module, exports) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n\n  var _class, _temp;\n\n  function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  }\n\n  var _createClass = function () {\n    function defineProperties(target, props) {\n      for (var i = 0; i < props.length; i++) {\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n      }\n    }\n\n    return function (Constructor, protoProps, staticProps) {\n      if (protoProps) defineProperties(Constructor.prototype, protoProps);\n      if (staticProps) defineProperties(Constructor, staticProps);\n      return Constructor;\n    };\n  }();\n\n  function isIn(needle, haystack) {\n    return haystack.indexOf(needle) >= 0;\n  }\n\n  function extend(custom, defaults) {\n    for (var key in defaults) {\n      if (custom[key] == null) {\n        var value = defaults[key];\n        custom[key] = value;\n      }\n    }\n    return custom;\n  }\n\n  function isMobile(agent) {\n    return (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(agent)\n    );\n  }\n\n  function createEvent(event) {\n    var bubble = arguments.length <= 1 || arguments[1] === undefined ? false : arguments[1];\n    var cancel = arguments.length <= 2 || arguments[2] === undefined ? false : arguments[2];\n    var detail = arguments.length <= 3 || arguments[3] === undefined ? null : arguments[3];\n\n    var customEvent = void 0;\n    if (document.createEvent != null) {\n      // W3C DOM\n      customEvent = document.createEvent('CustomEvent');\n      customEvent.initCustomEvent(event, bubble, cancel, detail);\n    } else if (document.createEventObject != null) {\n      // IE DOM < 9\n      customEvent = document.createEventObject();\n      customEvent.eventType = event;\n    } else {\n      customEvent.eventName = event;\n    }\n\n    return customEvent;\n  }\n\n  function emitEvent(elem, event) {\n    if (elem.dispatchEvent != null) {\n      // W3C DOM\n      elem.dispatchEvent(event);\n    } else if (event in (elem != null)) {\n      elem[event]();\n    } else if ('on' + event in (elem != null)) {\n      elem['on' + event]();\n    }\n  }\n\n  function addEvent(elem, event, fn) {\n    if (elem.addEventListener != null) {\n      // W3C DOM\n      elem.addEventListener(event, fn, false);\n    } else if (elem.attachEvent != null) {\n      // IE DOM\n      elem.attachEvent('on' + event, fn);\n    } else {\n      // fallback\n      elem[event] = fn;\n    }\n  }\n\n  function removeEvent(elem, event, fn) {\n    if (elem.removeEventListener != null) {\n      // W3C DOM\n      elem.removeEventListener(event, fn, false);\n    } else if (elem.detachEvent != null) {\n      // IE DOM\n      elem.detachEvent('on' + event, fn);\n    } else {\n      // fallback\n      delete elem[event];\n    }\n  }\n\n  function getInnerHeight() {\n    if ('innerHeight' in window) {\n      return window.innerHeight;\n    }\n\n    return document.documentElement.clientHeight;\n  }\n\n  // Minimalistic WeakMap shim, just in case.\n  var WeakMap = window.WeakMap || window.MozWeakMap || function () {\n    function WeakMap() {\n      _classCallCheck(this, WeakMap);\n\n      this.keys = [];\n      this.values = [];\n    }\n\n    _createClass(WeakMap, [{\n      key: 'get',\n      value: function get(key) {\n        for (var i = 0; i < this.keys.length; i++) {\n          var item = this.keys[i];\n          if (item === key) {\n            return this.values[i];\n          }\n        }\n        return undefined;\n      }\n    }, {\n      key: 'set',\n      value: function set(key, value) {\n        for (var i = 0; i < this.keys.length; i++) {\n          var item = this.keys[i];\n          if (item === key) {\n            this.values[i] = value;\n            return this;\n          }\n        }\n        this.keys.push(key);\n        this.values.push(value);\n        return this;\n      }\n    }]);\n\n    return WeakMap;\n  }();\n\n  // Dummy MutationObserver, to avoid raising exceptions.\n  var MutationObserver = window.MutationObserver || window.WebkitMutationObserver || window.MozMutationObserver || (_temp = _class = function () {\n    function MutationObserver() {\n      _classCallCheck(this, MutationObserver);\n\n      if (typeof console !== 'undefined' && console !== null) {\n        console.warn('MutationObserver is not supported by your browser.');\n        console.warn('WOW.js cannot detect dom mutations, please call .sync() after loading new content.');\n      }\n    }\n\n    _createClass(MutationObserver, [{\n      key: 'observe',\n      value: function observe() {}\n    }]);\n\n    return MutationObserver;\n  }(), _class.notSupported = true, _temp);\n\n  // getComputedStyle shim, from http://stackoverflow.com/a/21797294\n  var getComputedStyle = window.getComputedStyle || function getComputedStyle(el) {\n    var getComputedStyleRX = /(\\-([a-z]){1})/g;\n    return {\n      getPropertyValue: function getPropertyValue(prop) {\n        if (prop === 'float') {\n          prop = 'styleFloat';\n        }\n        if (getComputedStyleRX.test(prop)) {\n          prop.replace(getComputedStyleRX, function (_, _char) {\n            return _char.toUpperCase();\n          });\n        }\n        var currentStyle = el.currentStyle;\n\n        return (currentStyle != null ? currentStyle[prop] : void 0) || null;\n      }\n    };\n  };\n\n  var WOW = function () {\n    function WOW() {\n      var options = arguments.length <= 0 || arguments[0] === undefined ? {} : arguments[0];\n\n      _classCallCheck(this, WOW);\n\n      this.defaults = {\n        boxClass: 'wow',\n        animateClass: 'animated',\n        offset: 0,\n        mobile: true,\n        live: true,\n        callback: null,\n        scrollContainer: null\n      };\n\n      this.animate = function animateFactory() {\n        if ('requestAnimationFrame' in window) {\n          return function (callback) {\n            return window.requestAnimationFrame(callback);\n          };\n        }\n        return function (callback) {\n          return callback();\n        };\n      }();\n\n      this.vendors = ['moz', 'webkit'];\n\n      this.start = this.start.bind(this);\n      this.resetAnimation = this.resetAnimation.bind(this);\n      this.scrollHandler = this.scrollHandler.bind(this);\n      this.scrollCallback = this.scrollCallback.bind(this);\n      this.scrolled = true;\n      this.config = extend(options, this.defaults);\n      if (options.scrollContainer != null) {\n        this.config.scrollContainer = document.querySelector(options.scrollContainer);\n      }\n      // Map of elements to animation names:\n      this.animationNameCache = new WeakMap();\n      this.wowEvent = createEvent(this.config.boxClass);\n    }\n\n    _createClass(WOW, [{\n      key: 'init',\n      value: function init() {\n        this.element = window.document.documentElement;\n        if (isIn(document.readyState, ['interactive', 'complete'])) {\n          this.start();\n        } else {\n          addEvent(document, 'DOMContentLoaded', this.start);\n        }\n        this.finished = [];\n      }\n    }, {\n      key: 'start',\n      value: function start() {\n        var _this = this;\n\n        this.stopped = false;\n        this.boxes = [].slice.call(this.element.querySelectorAll('.' + this.config.boxClass));\n        this.all = this.boxes.slice(0);\n        if (this.boxes.length) {\n          if (this.disabled()) {\n            this.resetStyle();\n          } else {\n            for (var i = 0; i < this.boxes.length; i++) {\n              var box = this.boxes[i];\n              this.applyStyle(box, true);\n            }\n          }\n        }\n        if (!this.disabled()) {\n          addEvent(this.config.scrollContainer || window, 'scroll', this.scrollHandler);\n          addEvent(window, 'resize', this.scrollHandler);\n          this.interval = setInterval(this.scrollCallback, 50);\n        }\n        if (this.config.live) {\n          var mut = new MutationObserver(function (records) {\n            for (var j = 0; j < records.length; j++) {\n              var record = records[j];\n              for (var k = 0; k < record.addedNodes.length; k++) {\n                var node = record.addedNodes[k];\n                _this.doSync(node);\n              }\n            }\n            return undefined;\n          });\n          mut.observe(document.body, {\n            childList: true,\n            subtree: true\n          });\n        }\n      }\n    }, {\n      key: 'stop',\n      value: function stop() {\n        this.stopped = true;\n        removeEvent(this.config.scrollContainer || window, 'scroll', this.scrollHandler);\n        removeEvent(window, 'resize', this.scrollHandler);\n        if (this.interval != null) {\n          clearInterval(this.interval);\n        }\n      }\n    }, {\n      key: 'sync',\n      value: function sync() {\n        if (MutationObserver.notSupported) {\n          this.doSync(this.element);\n        }\n      }\n    }, {\n      key: 'doSync',\n      value: function doSync(element) {\n        if (typeof element === 'undefined' || element === null) {\n          element = this.element;\n        }\n        if (element.nodeType !== 1) {\n          return;\n        }\n        element = element.parentNode || element;\n        var iterable = element.querySelectorAll('.' + this.config.boxClass);\n        for (var i = 0; i < iterable.length; i++) {\n          var box = iterable[i];\n          if (!isIn(box, this.all)) {\n            this.boxes.push(box);\n            this.all.push(box);\n            if (this.stopped || this.disabled()) {\n              this.resetStyle();\n            } else {\n              this.applyStyle(box, true);\n            }\n            this.scrolled = true;\n          }\n        }\n      }\n    }, {\n      key: 'show',\n      value: function show(box) {\n        this.applyStyle(box);\n        box.className = box.className + ' ' + this.config.animateClass;\n        if (this.config.callback != null) {\n          this.config.callback(box);\n        }\n        emitEvent(box, this.wowEvent);\n\n        addEvent(box, 'animationend', this.resetAnimation);\n        addEvent(box, 'oanimationend', this.resetAnimation);\n        addEvent(box, 'webkitAnimationEnd', this.resetAnimation);\n        addEvent(box, 'MSAnimationEnd', this.resetAnimation);\n\n        return box;\n      }\n    }, {\n      key: 'applyStyle',\n      value: function applyStyle(box, hidden) {\n        var _this2 = this;\n\n        var duration = box.getAttribute('data-wow-duration');\n        var delay = box.getAttribute('data-wow-delay');\n        var iteration = box.getAttribute('data-wow-iteration');\n\n        return this.animate(function () {\n          return _this2.customStyle(box, hidden, duration, delay, iteration);\n        });\n      }\n    }, {\n      key: 'resetStyle',\n      value: function resetStyle() {\n        for (var i = 0; i < this.boxes.length; i++) {\n          var box = this.boxes[i];\n          box.style.visibility = 'visible';\n        }\n        return undefined;\n      }\n    }, {\n      key: 'resetAnimation',\n      value: function resetAnimation(event) {\n        if (event.type.toLowerCase().indexOf('animationend') >= 0) {\n          var target = event.target || event.srcElement;\n          target.className = target.className.replace(this.config.animateClass, '').trim();\n        }\n      }\n    }, {\n      key: 'customStyle',\n      value: function customStyle(box, hidden, duration, delay, iteration) {\n        if (hidden) {\n          this.cacheAnimationName(box);\n        }\n        box.style.visibility = hidden ? 'hidden' : 'visible';\n\n        if (duration) {\n          this.vendorSet(box.style, { animationDuration: duration });\n        }\n        if (delay) {\n          this.vendorSet(box.style, { animationDelay: delay });\n        }\n        if (iteration) {\n          this.vendorSet(box.style, { animationIterationCount: iteration });\n        }\n        this.vendorSet(box.style, { animationName: hidden ? 'none' : this.cachedAnimationName(box) });\n\n        return box;\n      }\n    }, {\n      key: 'vendorSet',\n      value: function vendorSet(elem, properties) {\n        for (var name in properties) {\n          if (properties.hasOwnProperty(name)) {\n            var value = properties[name];\n            elem['' + name] = value;\n            for (var i = 0; i < this.vendors.length; i++) {\n              var vendor = this.vendors[i];\n              elem['' + vendor + name.charAt(0).toUpperCase() + name.substr(1)] = value;\n            }\n          }\n        }\n      }\n    }, {\n      key: 'vendorCSS',\n      value: function vendorCSS(elem, property) {\n        var style = getComputedStyle(elem);\n        var result = style.getPropertyCSSValue(property);\n        for (var i = 0; i < this.vendors.length; i++) {\n          var vendor = this.vendors[i];\n          result = result || style.getPropertyCSSValue('-' + vendor + '-' + property);\n        }\n        return result;\n      }\n    }, {\n      key: 'animationName',\n      value: function animationName(box) {\n        var aName = void 0;\n        try {\n          aName = this.vendorCSS(box, 'animation-name').cssText;\n        } catch (error) {\n          // Opera, fall back to plain property value\n          aName = getComputedStyle(box).getPropertyValue('animation-name');\n        }\n\n        if (aName === 'none') {\n          return ''; // SVG/Firefox, unable to get animation name?\n        }\n\n        return aName;\n      }\n    }, {\n      key: 'cacheAnimationName',\n      value: function cacheAnimationName(box) {\n        // https://bugzilla.mozilla.org/show_bug.cgi?id=921834\n        // box.dataset is not supported for SVG elements in Firefox\n        return this.animationNameCache.set(box, this.animationName(box));\n      }\n    }, {\n      key: 'cachedAnimationName',\n      value: function cachedAnimationName(box) {\n        return this.animationNameCache.get(box);\n      }\n    }, {\n      key: 'scrollHandler',\n      value: function scrollHandler() {\n        this.scrolled = true;\n      }\n    }, {\n      key: 'scrollCallback',\n      value: function scrollCallback() {\n        if (this.scrolled) {\n          this.scrolled = false;\n          var results = [];\n          for (var i = 0; i < this.boxes.length; i++) {\n            var box = this.boxes[i];\n            if (box) {\n              if (this.isVisible(box)) {\n                this.show(box);\n                continue;\n              }\n              results.push(box);\n            }\n          }\n          this.boxes = results;\n          if (!this.boxes.length && !this.config.live) {\n            this.stop();\n          }\n        }\n      }\n    }, {\n      key: 'offsetTop',\n      value: function offsetTop(element) {\n        // SVG elements don't have an offsetTop in Firefox.\n        // This will use their nearest parent that has an offsetTop.\n        // Also, using ('offsetTop' of element) causes an exception in Firefox.\n        while (element.offsetTop === undefined) {\n          element = element.parentNode;\n        }\n        var top = element.offsetTop;\n        while (element.offsetParent) {\n          element = element.offsetParent;\n          top += element.offsetTop;\n        }\n        return top;\n      }\n    }, {\n      key: 'isVisible',\n      value: function isVisible(box) {\n        var offset = box.getAttribute('data-wow-offset') || this.config.offset;\n        var viewTop = this.config.scrollContainer && this.config.scrollContainer.scrollTop || window.pageYOffset;\n        var viewBottom = viewTop + Math.min(this.element.clientHeight, getInnerHeight()) - offset;\n        var top = this.offsetTop(box);\n        var bottom = top + box.clientHeight;\n\n        return top <= viewBottom && bottom >= viewTop;\n      }\n    }, {\n      key: 'disabled',\n      value: function disabled() {\n        return !this.config.mobile && isMobile(navigator.userAgent);\n      }\n    }]);\n\n    return WOW;\n  }();\n\n  exports.default = WOW;\n  module.exports = exports['default'];\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/wow.js/dist/wow.js\n"));

/***/ })

}]);