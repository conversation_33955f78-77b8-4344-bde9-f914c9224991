/* Related Products - Sharp Rectangular Design */
.relatedProducts {
  margin: 4rem 0;
  padding: 2rem 0;
  border-top: 2px solid #ddd;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.sectionTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.scrollControls {
  display: flex;
  gap: 0.5rem;
}

.scrollButton {
  background: #2563eb;
  color: white;
  border: 2px solid #2563eb;
  width: 45px;
  height: 45px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.scrollButton:hover:not(.disabled) {
  background: white;
  color: #2563eb;
}

.scrollButton.disabled {
  background: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
  opacity: 0.5;
}

/* Products Container */
.productsContainer {
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.productsContainer::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.productsGrid {
  display: flex;
  gap: 1.5rem;
  padding-bottom: 1rem;
}

/* Product Card */
.productCard {
  flex: 0 0 300px;
  background: white;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  overflow: hidden;
  transition: all 0.3s ease;
}

.productCard:hover {
  border-color: #2563eb;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.15);
}

.productLink {
  text-decoration: none;
  color: inherit;
  display: block;
}

/* Image Container */
.imageContainer {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  background: #f8f8f8;
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.productCard:hover .productImage {
  transform: scale(1.05);
}

.outOfStockOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
}

/* Product Info */
.productInfo {
  padding: 1.5rem;
}

.productName {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.productDetails {
  margin-bottom: 1rem;
}

.productCode {
  display: block;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #666;
  background: #f5f5f5;
  padding: 0.25rem 0.5rem;
  border: 1px solid #ddd;
  border-radius: 0; /* Sharp corners */
  margin-bottom: 0.75rem;
  width: fit-content;
}

.availability {
  font-size: 0.9rem;
}

.inStock {
  color: #28a745;
  font-weight: 600;
}

.outOfStock {
  color: #dc3545;
  font-weight: 600;
}

.priceContainer {
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.price {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1f2937;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .productCard {
    flex: 0 0 280px;
  }
  
  .sectionTitle {
    font-size: 1.75rem;
  }
}

@media (max-width: 768px) {
  .relatedProducts {
    margin: 3rem 0;
  }
  
  .sectionHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .scrollControls {
    justify-content: center;
  }
  
  .productCard {
    flex: 0 0 250px;
  }
  
  .productInfo {
    padding: 1rem;
  }
  
  .productName {
    font-size: 1.1rem;
  }
  
  .price {
    font-size: 1.2rem;
  }
  
  .sectionTitle {
    font-size: 1.5rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .productCard {
    flex: 0 0 220px;
  }
  
  .productInfo {
    padding: 0.75rem;
  }
  
  .productName {
    font-size: 1rem;
  }
  
  .price {
    font-size: 1.1rem;
  }
  
  .productCode {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
  }
  
  .scrollButton {
    width: 40px;
    height: 40px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .productCard {
    transition: none;
  }
  
  .productCard:hover {
    transform: none;
  }
  
  .productImage {
    transition: none;
  }
  
  .productCard:hover .productImage {
    transform: none;
  }
}
