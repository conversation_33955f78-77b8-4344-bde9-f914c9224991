"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/our-story/page",{

/***/ "(app-pages-browser)/./src/app/our-story/page.tsx":
/*!************************************!*\
  !*** ./src/app/our-story/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ourStory.module.css */ \"(app-pages-browser)/./src/app/our-story/ourStory.module.css\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OurStoryPage = ()=>{\n    _s();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Banner images array\n    const bannerImages = [\n        '/images/CollectionBackground.jpg',\n        '/images/CollectionBackground2.jpg',\n        '/images/CollectionBackground3.jpg',\n        '/images/catalog-banner-bg.jpg'\n    ];\n    // Auto-rotate banner images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OurStoryPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"OurStoryPage.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"OurStoryPage.useEffect.interval\": (prevIndex)=>(prevIndex + 1) % bannerImages.length\n                    }[\"OurStoryPage.useEffect.interval\"]);\n                }\n            }[\"OurStoryPage.useEffect.interval\"], 5000); // Change image every 5 seconds\n            return ({\n                \"OurStoryPage.useEffect\": ()=>clearInterval(interval)\n            })[\"OurStoryPage.useEffect\"];\n        }\n    }[\"OurStoryPage.useEffect\"], [\n        bannerImages.length\n    ]);\n    // Timeline data with images\n    const timelineData = [\n        {\n            year: '2010',\n            title: 'Foundation',\n            description: 'Cast Stone was founded with a vision to revolutionize architectural stone design and manufacturing. Our journey began with a commitment to excellence and innovation in every piece we create.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2012',\n            title: 'First Major Project',\n            description: 'Completed our first large-scale commercial project, establishing our reputation in the industry. This milestone project showcased our capabilities and set the foundation for future growth.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2015',\n            title: 'Innovation Breakthrough',\n            description: 'Developed proprietary casting techniques that enhanced durability and aesthetic appeal. Our research and development team achieved breakthrough innovations that set new industry standards.',\n            image: '/images/CollectionBackground3.jpg'\n        },\n        {\n            year: '2018',\n            title: 'International Expansion',\n            description: 'Expanded operations internationally, bringing our expertise to global markets. We established partnerships worldwide and began serving clients across multiple continents.',\n            image: '/images/catalog-banner-bg.jpg'\n        },\n        {\n            year: '2020',\n            title: 'Sustainable Practices',\n            description: 'Implemented eco-friendly manufacturing processes and sustainable material sourcing. Our commitment to environmental responsibility became a cornerstone of our operations.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2023',\n            title: 'Digital Innovation',\n            description: 'Launched advanced digital design tools and virtual consultation services. We embraced technology to enhance customer experience and streamline our design process.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2024',\n            title: 'Industry Leadership',\n            description: 'Recognized as industry leader with over 500 successful projects worldwide. Our dedication to quality and innovation has established us as the premier choice for architectural stone solutions.',\n            image: '/images/CollectionBackground3.jpg'\n        }\n    ];\n    // Scroll to next section\n    const scrollToTimeline = ()=>{\n        const timelineSection = document.getElementById('timeline-section');\n        if (timelineSection) {\n            timelineSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Navigate timeline\n    const navigateTimeline = (direction)=>{\n        if (direction === 'prev' && selectedYear > 0) {\n            setSelectedYear(selectedYear - 1);\n        } else if (direction === 'next' && selectedYear < timelineData.length - 1) {\n            setSelectedYear(selectedYear + 1);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().storyPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().bannerContainer),\n                    children: [\n                        bannerImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthImageContainer),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthLayer),\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 1.2,\n                                            rotateY: -15\n                                        },\n                                        animate: {\n                                            opacity: index === currentImageIndex ? 0.3 : 0,\n                                            scale: index === currentImageIndex ? 1.3 : 1.2,\n                                            rotateY: index === currentImageIndex ? -10 : -15,\n                                            filter: 'blur(8px)'\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            ease: \"easeInOut\"\n                                        },\n                                        style: {\n                                            backgroundImage: \"url(\".concat(image, \")\"),\n                                            zIndex: index === currentImageIndex ? 1 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthLayer),\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 1.1,\n                                            rotateY: -10\n                                        },\n                                        animate: {\n                                            opacity: index === currentImageIndex ? 0.6 : 0,\n                                            scale: index === currentImageIndex ? 1.2 : 1.1,\n                                            rotateY: index === currentImageIndex ? -5 : -10,\n                                            filter: 'blur(4px)'\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            ease: \"easeInOut\",\n                                            delay: 0.2\n                                        },\n                                        style: {\n                                            backgroundImage: \"url(\".concat(image, \")\"),\n                                            zIndex: index === currentImageIndex ? 2 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthLayer),\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 1,\n                                            rotateY: -5\n                                        },\n                                        animate: {\n                                            opacity: index === currentImageIndex ? 1 : 0,\n                                            scale: index === currentImageIndex ? 1.05 : 1,\n                                            rotateY: index === currentImageIndex ? 0 : -5,\n                                            filter: 'blur(0px)'\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            ease: \"easeInOut\",\n                                            delay: 0.4\n                                        },\n                                        style: {\n                                            backgroundImage: \"url(\".concat(image, \")\"),\n                                            zIndex: index === currentImageIndex ? 3 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTextContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.5\n                                            },\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSubtitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.8\n                                            },\n                                            children: \"In 2010, the world of architectural stone made the discovery of a new brand.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().scrollArrow),\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        delay: 1.2\n                                    },\n                                    onClick: scrollToTimeline,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowIcon),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M7 10L12 15L17 10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowText),\n                                            children: \"Explore Our Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"timeline-section\",\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineBackground),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTitle),\n                                    children: \"TIMELINE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearNavigation),\n                                    children: [\n                                        timelineData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButton), \" \").concat(index === selectedYear ? (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButtonActive) : ''),\n                                                onClick: ()=>setSelectedYear(index),\n                                                children: item.year\n                                            }, item.year, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().moreButton),\n                                            children: [\n                                                \"More\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"16\",\n                                                    height: \"16\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M19 9L12 16L5 9\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContentContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContent),\n                                    initial: {\n                                        opacity: 0,\n                                        x: 50\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImageContainer),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: timelineData[selectedYear].image,\n                                                alt: timelineData[selectedYear].title,\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImage),\n                                                width: 600,\n                                                height: 400,\n                                                style: {\n                                                    objectFit: 'cover'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTextContainer),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineYear),\n                                                    children: timelineData[selectedYear].year\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineItemTitle),\n                                                    children: timelineData[selectedYear].title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineDescription),\n                                                    children: timelineData[selectedYear].description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, selectedYear, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineNavigation),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().prevButton)),\n                                            onClick: ()=>navigateTimeline('prev'),\n                                            disabled: selectedYear === 0,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M15 18L9 12L15 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().nextButton)),\n                                            onClick: ()=>navigateTimeline('next'),\n                                            disabled: selectedYear === timelineData.length - 1,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 18L15 12L9 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().parallaxContainer),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionSection),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionContainer),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/CollectionBackground.jpg\",\n                                            alt: \"Vision of Architectural Excellence\",\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                            width: 600,\n                                            height: 400,\n                                            style: {\n                                                objectFit: 'cover'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                                children: \"A VISION OF ARCHITECTURAL EXCELLENCE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"At the age of fifty, Cast Stone decided to create its own brand, with the idea of pushing watchmaking beyond anything that existed at the time, with a new contemporary approach to horology. We was planning to develop one product: the watch of his dreams, an approach that involved operating with little regard for production costs, which were excessive.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"When released in 2001, this extraordinary timepiece with its ergonomic tonneau case design punctuated with distinctive torque screws and a compelling six-digit price tag, immediately placed the fledgling brand at the highest summit of the entire luxury watch market.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItemReverse)),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                                children: \"INNOVATION THROUGH CRAFTSMANSHIP\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"Our commitment to innovation extends beyond traditional boundaries. Each piece we create represents a perfect fusion of time-honored craftsmanship techniques and cutting-edge technology. This approach allows us to achieve unprecedented levels of precision and aesthetic refinement.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"The result is a collection of architectural stone products that not only meet the most demanding technical specifications but also inspire architects and designers to explore new possibilities in their creative endeavors. Every project becomes a testament to our unwavering pursuit of excellence.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/CollectionBackground2.jpg\",\n                                            alt: \"Innovation Through Craftsmanship\",\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                            width: 600,\n                                            height: 400,\n                                            style: {\n                                                objectFit: 'cover'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteSection),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteContent),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quote),\n                                                    children: \"“For a long time, I wished to create something extraordinary in architectural stone. I wanted to develop a new approach, far removed from traditional manufacturing methods, something totally innovative. My goal was to establish a new standard of excellence within the architectural stone industry, and I was very eager to see what could be achieved!”\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cite\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteAuthor),\n                                                    children: \"CAST STONE FOUNDER\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteTextContent),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteTitle),\n                                                    children: \"THE FRUITION OF DECADES OF EXPERIENCE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteText),\n                                                    children: \"For Cast Stone, this was not an impulsive decision quickly taken; it was the direct fruition of decades of experience gained through diverse architectural projects and luxury material development. Our deep fascination for innovative manufacturing techniques, expertise in material science, and personal passion for architectural excellence combined with our extreme sensitivity to design and functionality, meant that no existing stone products could completely meet our vision for perfection.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OurStoryPage, \"yIsq3P1r70jr1ywORtZUlvMWrG4=\");\n_c = OurStoryPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OurStoryPage);\nvar _c;\n$RefreshReg$(_c, \"OurStoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/our-story/page.tsx\n"));

/***/ })

});