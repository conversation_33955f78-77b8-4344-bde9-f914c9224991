.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
}

.loginCard {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loginHeader {
  padding: 2rem 2rem 1rem;
  text-align: center;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.loginHeader h2 {
  margin: 0 0 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.loginHeader p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

.loginForm {
  padding: 2rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
  font-size: 0.875rem;
}

.formGroup input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.formGroup input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formGroup input.error {
  border-color: #ef4444;
}

.formGroup input.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.formGroup input:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
}

.errorText {
  display: block;
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.loginButton {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 0.5rem;
}

.loginButton:hover:not(:disabled) {
  background: #2563eb;
}

.loginButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.loginFooter {
  padding: 1.5rem 2rem 2rem;
  text-align: center;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.loginFooter p {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.linkButton {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
  padding: 0;
  transition: color 0.2s ease;
}

.linkButton:hover:not(:disabled) {
  color: #2563eb;
}

.linkButton:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 480px) {
  .loginContainer {
    padding: 1rem;
  }
  
  .loginCard {
    max-width: none;
  }
  
  .loginHeader {
    padding: 1.5rem 1.5rem 1rem;
  }
  
  .loginForm {
    padding: 1.5rem;
  }
  
  .loginFooter {
    padding: 1rem 1.5rem 1.5rem;
  }
}
