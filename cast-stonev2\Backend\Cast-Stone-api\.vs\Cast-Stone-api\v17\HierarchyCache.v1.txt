﻿++Solution 'Cast-Stone-api' ‎ (1 of 1 project)
i:{00000000-0000-0000-0000-000000000000}:Cast-Stone-api.sln
++Cast-Stone-api
i:{00000000-0000-0000-0000-000000000000}:Cast-Stone-api
++Connected Services 
i:{************************************}:>770
++Dependencies
i:{************************************}:>772
++Properties
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\properties\
++launchSettings.json
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\properties\launchsettings.json
++Controllers
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\
++CartController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\cartcontroller.cs
++CloudinaryController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\cloudinarycontroller.cs
++CollectionsController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\collectionscontroller.cs
++ContactFormController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\contactformcontroller.cs
++DownloadableContentController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\downloadablecontentcontroller.cs
++OrdersController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\orderscontroller.cs
++PaymentsController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\paymentscontroller.cs
++ProductDetailsController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\productdetailscontroller.cs
++ProductsController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\productscontroller.cs
++ProductSpecificationsController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\productspecificationscontroller.cs
++SeedController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\seedcontroller.cs
++SMTPController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\smtpcontroller.cs
++UsersController.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\controllers\userscontroller.cs
++Data
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\data\
++ApplicationDbContext.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\data\applicationdbcontext.cs
++Domain
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\
++Models
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\
++PaymentGatewaySettings
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\paymentgatewaysettings\
++Cart.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\cart.cs
++CartItem.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\cartitem.cs
++Collection.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\collection.cs
++ContactFormSubmission.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\contactformsubmission.cs
++DownloadableContent.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\downloadablecontent.cs
++Order.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\order.cs
++OrderItem.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\orderitem.cs
++Product.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\product.cs
++ProductDetails.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\productdetails.cs
++ProductSpecifications.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\productspecifications.cs
++Status.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\status.cs
++Subscription.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\subscription.cs
++User.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\user.cs
++DTOs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\
++Request
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\
++Response
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\
++Mappings
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\mappings\
++MappingProfile.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\mappings\mappingprofile.cs
++Migrations
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\
++Repositories
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\
++Implementations
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\
++BaseRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\baserepository.cs
++CartRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\cartrepository.cs
++CollectionRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\collectionrepository.cs
++ContactFormSubmissionRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\contactformsubmissionrepository.cs
++DownloadableContentRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\downloadablecontentrepository.cs
++OrderRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\orderrepository.cs
++ProductDetailsRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\productdetailsrepository.cs
++ProductRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\productrepository.cs
++ProductSpecificationsRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\productspecificationsrepository.cs
++UserRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\implementations\userrepository.cs
++Interfaces
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\
++Scripts
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\scripts\
++FixJsonData.sql
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\scripts\fixjsondata.sql
++FixJsonDataRunner.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\scripts\fixjsondatarunner.cs
++RunSeeding.ps1
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\scripts\runseeding.ps1
++SeedData.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\scripts\seeddata.cs
++SeedDataRunner.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\scripts\seeddatarunner.cs
++Services
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\
++CartService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\cartservice.cs
++CollectionService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\collectionservice.cs
++ContactFormSubmissionService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\contactformsubmissionservice.cs
++DownloadableContentService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\downloadablecontentservice.cs
++OrderService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\orderservice.cs
++ProductDetailsService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\productdetailsservice.cs
++ProductService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\productservice.cs
++ProductSpecificationsService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\productspecificationsservice.cs
++UserService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\implementations\userservice.cs
++CloudinaryService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\cloudinaryservice.cs
++EmailService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\emailservice.cs
++PayPalService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\paypalservice.cs
++StripeService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\stripeservice.cs
++API_PAYLOADS.txt
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\api_payloads.txt
++appsettings.json
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\appsettings.json
++appsettings.Development.json
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\appsettings.development.json
++appsettings.Production.json
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\appsettings.production.json
++Cast-Stone-api.http
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\cast-stone-api.http
++COLLECTION_RELATIONSHIPS.md
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\collection_relationships.md
++docker.txt
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\docker.txt
++EMAIL_TESTING_GUIDE.md
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\email_testing_guide.md
++Program.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\program.cs
++railway.json
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\railway.json
++RAILWAY_DEPLOYMENT_GUIDE.md
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\railway_deployment_guide.md
++README.md
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\readme.md
++TestApiEndpoints.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\testapiendpoints.cs
++TestConnection.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\testconnection.cs
++No service dependencies discovered
i:{************************************}:>771
++Analyzers
i:{************************************}:>781
++Frameworks
i:{************************************}:>804
++Packages
i:{************************************}:>807
++PayPalSettings.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\paymentgatewaysettings\paypalsettings.cs
++StripeSettings.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\domain\models\paymentgatewaysettings\stripesettings.cs
++AddToCartRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\addtocartrequest.cs
++CreateCollectionRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\createcollectionrequest.cs
++CreateContactFormSubmissionRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\createcontactformsubmissionrequest.cs
++CreateDownloadableContentRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\createdownloadablecontentrequest.cs
++CreateOrderRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\createorderrequest.cs
++CreateProductDetailsRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\createproductdetailsrequest.cs
++CreateProductRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\createproductrequest.cs
++CreateProductSpecificationsRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\createproductspecificationsrequest.cs
++CreateUserRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\createuserrequest.cs
++EmailRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\emailrequest.cs
++FilterRequests.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\filterrequests.cs
++PaymentRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\paymentrequest.cs
++UpdateCartItemRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\updatecartitemrequest.cs
++UpdateCollectionRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\updatecollectionrequest.cs
++UpdateDownloadableContentRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\updatedownloadablecontentrequest.cs
++UpdateOrderStatusRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\updateorderstatusrequest.cs
++UpdateProductDetailsRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\updateproductdetailsrequest.cs
++UpdateProductRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\updateproductrequest.cs
++UpdateProductSpecificationsRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\updateproductspecificationsrequest.cs
++UpdateUserRequest.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\request\updateuserrequest.cs
++ApiResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\apiresponse.cs
++CartResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\cartresponse.cs
++CollectionResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\collectionresponse.cs
++ContactFormSubmissionResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\contactformsubmissionresponse.cs
++DownloadableContentResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\downloadablecontentresponse.cs
++OrderResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\orderresponse.cs
++PaymentResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\paymentresponse.cs
++ProductDetailsResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\productdetailsresponse.cs
++ProductResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\productresponse.cs
++ProductSpecificationsResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\productspecificationsresponse.cs
++UserResponse.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\dtos\response\userresponse.cs
++20250703213814_InitialModels.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250703213814_initialmodels.cs
++20250703215232_AddDomainModels.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250703215232_adddomainmodels.cs
++20250704101616_AddNameToUser.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250704101616_addnametouser.cs
++20250704103108_UpdateStatusData.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250704103108_updatestatusdata.cs
++20250706145450_AddCartTables.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250706145450_addcarttables.cs
++20250708081419_AddImagesToCollections.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250708081419_addimagestocollections.cs
++20250709155737_UpdateContactFormSubmissionFields.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250709155737_updatecontactformsubmissionfields.cs
++20250710175040_UpdateCollectionImageIds.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250710175040_updatecollectionimageids.cs
++20250710175830_AddProductIdsToCollections.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250710175830_addproductidstocollections.cs
++20250710182904_UpdateCollectionChildRelationships.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250710182904_updatecollectionchildrelationships.cs
++20250711151534_AddProductSpecificationsDetailsAndDownloadableContent.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250711151534_addproductspecificationsdetailsanddownloadablecontent.cs
++20250714120153_UpdatingSubProducts.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250714120153_updatingsubproducts.cs
++ApplicationDbContextModelSnapshot.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\applicationdbcontextmodelsnapshot.cs
++IBaseRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\ibaserepository.cs
++ICartRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\icartrepository.cs
++ICollectionRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\icollectionrepository.cs
++IContactFormSubmissionRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\icontactformsubmissionrepository.cs
++IDownloadableContentRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\idownloadablecontentrepository.cs
++IOrderRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\iorderrepository.cs
++IProductDetailsRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\iproductdetailsrepository.cs
++IProductRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\iproductrepository.cs
++IProductSpecificationsRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\iproductspecificationsrepository.cs
++IUserRepository.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\repositories\interfaces\iuserrepository.cs
++IBaseService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\ibaseservice.cs
++ICartService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\icartservice.cs
++ICollectionService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\icollectionservice.cs
++IContactFormSubmissionService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\icontactformsubmissionservice.cs
++IDownloadableContentService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\idownloadablecontentservice.cs
++IOrderService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\iorderservice.cs
++IProductDetailsService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\iproductdetailsservice.cs
++IProductService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\iproductservice.cs
++IProductSpecificationsService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\iproductspecificationsservice.cs
++IUserService.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\services\interfaces\iuserservice.cs
++Microsoft.AspNetCore.Analyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.Analyzers
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.3\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.Analyzers
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.3\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.EntityFrameworkCore.Analyzers
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\8.0.0\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.ObjectPool
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Collections.Immutable
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++System.Text.Json.SourceGeneration
i:{************************************}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.6\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.AspNetCore.App
i:{************************************}:>805
++Microsoft.NETCore.App
i:{************************************}:>806
++AutoMapper (12.0.1)
i:{************************************}:>815
++AutoMapper.Extensions.Microsoft.DependencyInjection (12.0.1)
i:{************************************}:>809
++BCrypt.Net-Next (4.0.3)
i:{************************************}:>814
++CloudinaryDotNet (1.27.6)
i:{************************************}:>821
++MailKit (4.13.0)
i:{************************************}:>820
++Microsoft.AspNetCore.OpenApi (8.0.0)
i:{************************************}:>817
++Microsoft.EntityFrameworkCore (8.0.0)
i:{************************************}:>808
++Microsoft.EntityFrameworkCore.Design (8.0.0)
i:{************************************}:>954
++Microsoft.Extensions.Configuration.Json (9.0.6)
i:{************************************}:>813
++Microsoft.VisualStudio.Azure.Containers.Tools.Targets (1.21.0)
i:{************************************}:>818
++Npgsql.EntityFrameworkCore.PostgreSQL (8.0.0)
i:{************************************}:>816
++PayPalCheckoutSdk (1.0.4)
i:{************************************}:>812
++Stripe.net (48.3.0)
i:{************************************}:>819
++Swashbuckle.AspNetCore (6.6.2)
i:{************************************}:>811
++20250703213814_InitialModels.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250703213814_initialmodels.designer.cs
++20250703215232_AddDomainModels.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250703215232_adddomainmodels.designer.cs
++20250704101616_AddNameToUser.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250704101616_addnametouser.designer.cs
++20250704103108_UpdateStatusData.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250704103108_updatestatusdata.designer.cs
++20250706145450_AddCartTables.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250706145450_addcarttables.designer.cs
++20250708081419_AddImagesToCollections.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250708081419_addimagestocollections.designer.cs
++20250709155737_UpdateContactFormSubmissionFields.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250709155737_updatecontactformsubmissionfields.designer.cs
++20250710175040_UpdateCollectionImageIds.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250710175040_updatecollectionimageids.designer.cs
++20250710175830_AddProductIdsToCollections.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250710175830_addproductidstocollections.designer.cs
++20250710182904_UpdateCollectionChildRelationships.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250710182904_updatecollectionchildrelationships.designer.cs
++20250711151534_AddProductSpecificationsDetailsAndDownloadableContent.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250711151534_addproductspecificationsdetailsanddownloadablecontent.designer.cs
++20250714120153_UpdatingSubProducts.Designer.cs
i:{************************************}:c:\users\<USER>\desktop\cast-stonev2\cast-stonev2\backend\cast-stone-api\migrations\20250714120153_updatingsubproducts.designer.cs
++appsettings.Local.json
++LOCAL_SETUP_GUIDE.md
++setup-local.ps1
++run-local.bat
++run-local.ps1
++EMAIL_DEBUG_GUIDE.md
