"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/our-story/page",{

/***/ "(app-pages-browser)/./src/app/our-story/page.tsx":
/*!************************************!*\
  !*** ./src/app/our-story/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ourStory.module.css */ \"(app-pages-browser)/./src/app/our-story/ourStory.module.css\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OurStoryPage = ()=>{\n    _s();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Banner images array\n    const bannerImages = [\n        '/images/CollectionBackground.jpg',\n        '/images/CollectionBackground2.jpg',\n        '/images/CollectionBackground3.jpg',\n        '/images/catalog-banner-bg.jpg'\n    ];\n    // Auto-rotate banner images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OurStoryPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"OurStoryPage.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"OurStoryPage.useEffect.interval\": (prevIndex)=>(prevIndex + 1) % bannerImages.length\n                    }[\"OurStoryPage.useEffect.interval\"]);\n                }\n            }[\"OurStoryPage.useEffect.interval\"], 5000); // Change image every 5 seconds\n            return ({\n                \"OurStoryPage.useEffect\": ()=>clearInterval(interval)\n            })[\"OurStoryPage.useEffect\"];\n        }\n    }[\"OurStoryPage.useEffect\"], [\n        bannerImages.length\n    ]);\n    // Timeline data with images\n    const timelineData = [\n        {\n            year: '2010',\n            title: 'Foundation',\n            description: 'Cast Stone was founded with a vision to revolutionize architectural stone design and manufacturing. Our journey began with a commitment to excellence and innovation in every piece we create.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2012',\n            title: 'First Major Project',\n            description: 'Completed our first large-scale commercial project, establishing our reputation in the industry. This milestone project showcased our capabilities and set the foundation for future growth.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2015',\n            title: 'Innovation Breakthrough',\n            description: 'Developed proprietary casting techniques that enhanced durability and aesthetic appeal. Our research and development team achieved breakthrough innovations that set new industry standards.',\n            image: '/images/CollectionBackground3.jpg'\n        },\n        {\n            year: '2018',\n            title: 'International Expansion',\n            description: 'Expanded operations internationally, bringing our expertise to global markets. We established partnerships worldwide and began serving clients across multiple continents.',\n            image: '/images/catalog-banner-bg.jpg'\n        },\n        {\n            year: '2020',\n            title: 'Sustainable Practices',\n            description: 'Implemented eco-friendly manufacturing processes and sustainable material sourcing. Our commitment to environmental responsibility became a cornerstone of our operations.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2023',\n            title: 'Digital Innovation',\n            description: 'Launched advanced digital design tools and virtual consultation services. We embraced technology to enhance customer experience and streamline our design process.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2024',\n            title: 'Industry Leadership',\n            description: 'Recognized as industry leader with over 500 successful projects worldwide. Our dedication to quality and innovation has established us as the premier choice for architectural stone solutions.',\n            image: '/images/CollectionBackground3.jpg'\n        }\n    ];\n    // Scroll to next section\n    const scrollToTimeline = ()=>{\n        const timelineSection = document.getElementById('timeline-section');\n        if (timelineSection) {\n            timelineSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Navigate timeline\n    const navigateTimeline = (direction)=>{\n        if (direction === 'prev' && selectedYear > 0) {\n            setSelectedYear(selectedYear - 1);\n        } else if (direction === 'next' && selectedYear < timelineData.length - 1) {\n            setSelectedYear(selectedYear + 1);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().storyPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().bannerContainer),\n                    children: [\n                        bannerImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthImageContainer),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthLayer),\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 1.2,\n                                            rotateY: -15\n                                        },\n                                        animate: {\n                                            opacity: index === currentImageIndex ? 0.3 : 0,\n                                            scale: index === currentImageIndex ? 1.3 : 1.2,\n                                            rotateY: index === currentImageIndex ? -10 : -15,\n                                            filter: 'blur(8px)'\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            ease: \"easeInOut\"\n                                        },\n                                        style: {\n                                            backgroundImage: \"url(\".concat(image, \")\"),\n                                            zIndex: index === currentImageIndex ? 1 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthLayer),\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 1.1,\n                                            rotateY: -10\n                                        },\n                                        animate: {\n                                            opacity: index === currentImageIndex ? 0.6 : 0,\n                                            scale: index === currentImageIndex ? 1.2 : 1.1,\n                                            rotateY: index === currentImageIndex ? -5 : -10,\n                                            filter: 'blur(4px)'\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            ease: \"easeInOut\",\n                                            delay: 0.2\n                                        },\n                                        style: {\n                                            backgroundImage: \"url(\".concat(image, \")\"),\n                                            zIndex: index === currentImageIndex ? 2 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().depthLayer),\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 1,\n                                            rotateY: -5\n                                        },\n                                        animate: {\n                                            opacity: index === currentImageIndex ? 1 : 0,\n                                            scale: index === currentImageIndex ? 1.05 : 1,\n                                            rotateY: index === currentImageIndex ? 0 : -5,\n                                            filter: 'blur(0px)'\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            ease: \"easeInOut\",\n                                            delay: 0.4\n                                        },\n                                        style: {\n                                            backgroundImage: \"url(\".concat(image, \")\"),\n                                            zIndex: index === currentImageIndex ? 3 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTextContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.5\n                                            },\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSubtitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.8\n                                            },\n                                            children: \"In 2010, the world of architectural stone made the discovery of a new brand.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().scrollArrow),\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        delay: 1.2\n                                    },\n                                    onClick: scrollToTimeline,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowIcon),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M7 10L12 15L17 10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowText),\n                                            children: \"Explore Our Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"timeline-section\",\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineBackground),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTitle),\n                                    children: \"TIMELINE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearNavigation),\n                                    children: [\n                                        timelineData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButton), \" \").concat(index === selectedYear ? (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButtonActive) : ''),\n                                                onClick: ()=>setSelectedYear(index),\n                                                children: item.year\n                                            }, item.year, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().moreButton),\n                                            children: [\n                                                \"More\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"16\",\n                                                    height: \"16\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M19 9L12 16L5 9\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContentContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContent),\n                                    initial: {\n                                        opacity: 0,\n                                        x: 50\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImageContainer),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: timelineData[selectedYear].image,\n                                                alt: timelineData[selectedYear].title,\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImage),\n                                                width: 600,\n                                                height: 400,\n                                                style: {\n                                                    objectFit: 'cover'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTextContainer),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineYear),\n                                                    children: timelineData[selectedYear].year\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineItemTitle),\n                                                    children: timelineData[selectedYear].title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineDescription),\n                                                    children: timelineData[selectedYear].description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, selectedYear, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineNavigation),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().prevButton)),\n                                            onClick: ()=>navigateTimeline('prev'),\n                                            disabled: selectedYear === 0,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M15 18L9 12L15 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().nextButton)),\n                                            onClick: ()=>navigateTimeline('next'),\n                                            disabled: selectedYear === timelineData.length - 1,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 18L15 12L9 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().parallaxContainer),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionSection),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionContainer),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/CollectionBackground.jpg\",\n                                            alt: \"Vision of Architectural Excellence\",\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                            width: 600,\n                                            height: 400,\n                                            style: {\n                                                objectFit: 'cover'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                                children: \"A VISION OF ARCHITECTURAL EXCELLENCE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"At the age of fifty, Cast Stone decided to create its own brand, with the idea of pushing watchmaking beyond anything that existed at the time, with a new contemporary approach to horology. We was planning to develop one product: the watch of his dreams, an approach that involved operating with little regard for production costs, which were excessive.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"When released in 2001, this extraordinary timepiece with its ergonomic tonneau case design punctuated with distinctive torque screws and a compelling six-digit price tag, immediately placed the fledgling brand at the highest summit of the entire luxury watch market.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItemReverse)),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                                children: \"INNOVATION THROUGH CRAFTSMANSHIP\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"Our commitment to innovation extends beyond traditional boundaries. Each piece we create represents a perfect fusion of time-honored craftsmanship techniques and cutting-edge technology. This approach allows us to achieve unprecedented levels of precision and aesthetic refinement.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                                children: \"The result is a collection of architectural stone products that not only meet the most demanding technical specifications but also inspire architects and designers to explore new possibilities in their creative endeavors. Every project becomes a testament to our unwavering pursuit of excellence.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"/images/CollectionBackground2.jpg\",\n                                            alt: \"Innovation Through Craftsmanship\",\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                            width: 600,\n                                            height: 400,\n                                            style: {\n                                                objectFit: 'cover'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteSection),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteContent),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quote),\n                                                    children: \"“For a long time, I wished to create something extraordinary in architectural stone. I wanted to develop a new approach, far removed from traditional manufacturing methods, something totally innovative. My goal was to establish a new standard of excellence within the architectural stone industry, and I was very eager to see what could be achieved!”\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cite\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteAuthor),\n                                                    children: \"CAST STONE FOUNDER\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteTextContent),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteTitle),\n                                                    children: \"THE FRUITION OF DECADES OF EXPERIENCE\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteText),\n                                                    children: \"For Cast Stone, this was not an impulsive decision quickly taken; it was the direct fruition of decades of experience gained through diverse architectural projects and luxury material development. Our deep fascination for innovative manufacturing techniques, expertise in material science, and personal passion for architectural excellence combined with our extreme sensitivity to design and functionality, meant that no existing stone products could completely meet our vision for perfection.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OurStoryPage, \"yIsq3P1r70jr1ywORtZUlvMWrG4=\");\n_c = OurStoryPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OurStoryPage);\nvar _c;\n$RefreshReg$(_c, \"OurStoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/our-story/page.tsx\n"));

/***/ })

});