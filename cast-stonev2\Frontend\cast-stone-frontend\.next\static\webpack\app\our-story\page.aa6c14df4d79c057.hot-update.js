"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/our-story/page",{

/***/ "(app-pages-browser)/./src/app/our-story/page.tsx":
/*!************************************!*\
  !*** ./src/app/our-story/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ourStory.module.css */ \"(app-pages-browser)/./src/app/our-story/ourStory.module.css\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst OurStoryPage = ()=>{\n    _s();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Banner images array\n    const bannerImages = [\n        '/images/CollectionBackground.jpg',\n        '/images/CollectionBackground2.jpg',\n        '/images/CollectionBackground3.jpg',\n        '/images/catalog-banner-bg.jpg'\n    ];\n    // Auto-rotate banner images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OurStoryPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"OurStoryPage.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"OurStoryPage.useEffect.interval\": (prevIndex)=>(prevIndex + 1) % bannerImages.length\n                    }[\"OurStoryPage.useEffect.interval\"]);\n                }\n            }[\"OurStoryPage.useEffect.interval\"], 5000); // Change image every 5 seconds\n            return ({\n                \"OurStoryPage.useEffect\": ()=>clearInterval(interval)\n            })[\"OurStoryPage.useEffect\"];\n        }\n    }[\"OurStoryPage.useEffect\"], [\n        bannerImages.length\n    ]);\n    // Timeline data with images\n    const timelineData = [\n        {\n            year: '2010',\n            title: 'Foundation',\n            description: 'Cast Stone was founded with a vision to revolutionize architectural stone design and manufacturing. Our journey began with a commitment to excellence and innovation in every piece we create.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2012',\n            title: 'First Major Project',\n            description: 'Completed our first large-scale commercial project, establishing our reputation in the industry. This milestone project showcased our capabilities and set the foundation for future growth.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2015',\n            title: 'Innovation Breakthrough',\n            description: 'Developed proprietary casting techniques that enhanced durability and aesthetic appeal. Our research and development team achieved breakthrough innovations that set new industry standards.',\n            image: '/images/CollectionBackground3.jpg'\n        },\n        {\n            year: '2018',\n            title: 'International Expansion',\n            description: 'Expanded operations internationally, bringing our expertise to global markets. We established partnerships worldwide and began serving clients across multiple continents.',\n            image: '/images/catalog-banner-bg.jpg'\n        },\n        {\n            year: '2020',\n            title: 'Sustainable Practices',\n            description: 'Implemented eco-friendly manufacturing processes and sustainable material sourcing. Our commitment to environmental responsibility became a cornerstone of our operations.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2023',\n            title: 'Digital Innovation',\n            description: 'Launched advanced digital design tools and virtual consultation services. We embraced technology to enhance customer experience and streamline our design process.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2024',\n            title: 'Industry Leadership',\n            description: 'Recognized as industry leader with over 500 successful projects worldwide. Our dedication to quality and innovation has established us as the premier choice for architectural stone solutions.',\n            image: '/images/CollectionBackground3.jpg'\n        }\n    ];\n    // Scroll to next section\n    const scrollToTimeline = ()=>{\n        const timelineSection = document.getElementById('timeline-section');\n        if (timelineSection) {\n            timelineSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Navigate timeline\n    const navigateTimeline = (direction)=>{\n        if (direction === 'prev' && selectedYear > 0) {\n            setSelectedYear(selectedYear - 1);\n        } else if (direction === 'next' && selectedYear < timelineData.length - 1) {\n            setSelectedYear(selectedYear + 1);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().storyPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().bannerContainer),\n                    children: [\n                        bannerImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().bannerImage),\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: index === currentImageIndex ? 1 : 0,\n                                    scale: index === currentImageIndex ? 1.05 : 1\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeInOut\"\n                                },\n                                style: {\n                                    backgroundImage: \"url(\".concat(image, \")\"),\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    width: '100%',\n                                    height: '100%',\n                                    backgroundSize: 'cover',\n                                    backgroundPosition: 'center',\n                                    zIndex: index === currentImageIndex ? 1 : 0\n                                }\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroOverlay),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroContainer),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroTitle),\n                                        initial: {\n                                            opacity: 0,\n                                            y: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 1,\n                                            delay: 0.5\n                                        },\n                                        children: \"Our Story\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroSubtitle),\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 1,\n                                            delay: 0.8\n                                        },\n                                        children: \"In 2010, the world of architectural stone made the discovery of a new brand.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().breadcrumbSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().container),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().breadcrumb),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"The Company\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().currentPage),\n                                children: \"Our Story\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().mainContent),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().container),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineSection),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().sectionHeader),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().sectionTitle),\n                                        children: \"Our Journey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineContainer),\n                                    children: timelineData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineItem),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineYear),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.year\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineDot)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineContent),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineTitle),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineDescription),\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, item.year, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionContent),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionTitle),\n                                        children: \"A vision of architectural excellence\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionText),\n                                        children: \"At Cast Stone, we believe that exceptional architecture begins with exceptional materials. Our journey started with a simple yet profound vision: to create cast stone products that not only meet the highest standards of quality and durability but also inspire architects and designers to push the boundaries of what's possible.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionText),\n                                        children: \"From our humble beginnings to becoming an industry leader, we have remained committed to innovation, craftsmanship, and sustainability. Every piece we create tells a story of dedication, precision, and artistic vision.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseContent),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseTitle),\n                                        children: \"The fruition of decades of experience\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseText),\n                                        children: \"Our expertise in cast stone manufacturing represents the culmination of years of research, development, and hands-on experience. We have mastered the art of combining traditional craftsmanship with cutting-edge technology to create products that stand the test of time.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseText),\n                                        children: \"This deep understanding of materials, combined with our passion for architectural innovation and our commitment to excellence, means that every Cast Stone product exceeds expectations in both form and function.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipContent),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipTitle),\n                                        children: \"Building lasting partnerships\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipText),\n                                        children: \"Our success is built on strong partnerships with architects, designers, contractors, and clients who share our vision for excellence. We work closely with each partner to understand their unique requirements and deliver solutions that exceed expectations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipText),\n                                        children: \"These collaborative relationships have been the foundation of our growth and continue to drive innovation in everything we do. Together, we create architectural masterpieces that define skylines and inspire communities.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().successSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().successContent),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().successTitle),\n                                        children: \"A successful formula\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().successText),\n                                        children: \"Those who understand the uncompromising quality of our visionary products have made Cast Stone an unequivocal success. Today, over a decade later, our product portfolio comprises more than 200 unique designs, each crafted with the same passion and uncompromising principles that guided our first creation.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OurStoryPage, \"yIsq3P1r70jr1ywORtZUlvMWrG4=\");\n_c = OurStoryPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OurStoryPage);\nvar _c;\n$RefreshReg$(_c, \"OurStoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/our-story/page.tsx\n"));

/***/ })

});