/* CTA Section Component */
.ctaSection {
  position: relative;
  height: 33vh; /* 1/3 height as requested */
  min-height: 300px;
  max-height: 500px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Background Container */
.backgroundContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}

.backgroundImage {
  object-fit: cover;
  object-position: center;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.ctaSection:hover .backgroundImage {
  transform: scale(1.02);
}

/* Overlay */
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: linear-gradient(
    135deg,
    rgba(30, 58, 138, 0.8) 0%,
    rgba(30, 64, 175, 0.7) 50%,
    rgba(37, 99, 235, 0.6) 100%
  ); */
  background: transparent;
  z-index: 1;
}

/* Content */
.content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.container {
  max-width: 800px;
  text-align: center;
  color: white;
}

/* Title */
.title {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* Description */
.description {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.95;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

/* CTA Button */
.ctaButton {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  animation: fadeInUp 0.8s ease-out 0.6s both;
  position: relative;
  overflow: hidden;
}

.ctaButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.ctaButton:hover::before {
  left: 100%;
}

.ctaButton:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.ctaButton:active {
  transform: translateY(0);
}

/* Button Icon */
.buttonIcon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.ctaButton:hover .buttonIcon {
  transform: translateX(4px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .ctaSection {
    height: 40vh;
    min-height: 280px;
  }
  
  .content {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .ctaSection {
    height: 45vh;
    min-height: 250px;
  }
  
  .content {
    padding: 1rem;
  }
  
  .title {
    margin-bottom: 0.75rem;
  }
  
  .description {
    margin-bottom: 1.5rem;
  }
  
  .ctaButton {
    padding: 0.875rem 1.75rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .ctaSection {
    height: 50vh;
    min-height: 220px;
  }
  
  .content {
    padding: 0.75rem;
  }
  
  .description {
    margin-bottom: 1.25rem;
  }
  
  .ctaButton {
    padding: 0.75rem 1.5rem;
    font-size: 0.85rem;
    gap: 0.5rem;
  }
  
  .buttonIcon {
    width: 18px;
    height: 18px;
  }
}
