"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/our-story/page",{

/***/ "(app-pages-browser)/./src/app/our-story/page.tsx":
/*!************************************!*\
  !*** ./src/app/our-story/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ourStory.module.css */ \"(app-pages-browser)/./src/app/our-story/ourStory.module.css\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OurStoryPage = ()=>{\n    _s();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Banner images array\n    const bannerImages = [\n        '/images/CollectionBackground.jpg',\n        '/images/CollectionBackground2.jpg',\n        '/images/CollectionBackground3.jpg',\n        '/images/catalog-banner-bg.jpg'\n    ];\n    // Auto-rotate banner images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OurStoryPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"OurStoryPage.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"OurStoryPage.useEffect.interval\": (prevIndex)=>(prevIndex + 1) % bannerImages.length\n                    }[\"OurStoryPage.useEffect.interval\"]);\n                }\n            }[\"OurStoryPage.useEffect.interval\"], 5000); // Change image every 5 seconds\n            return ({\n                \"OurStoryPage.useEffect\": ()=>clearInterval(interval)\n            })[\"OurStoryPage.useEffect\"];\n        }\n    }[\"OurStoryPage.useEffect\"], [\n        bannerImages.length\n    ]);\n    // Timeline data with images\n    const timelineData = [\n        {\n            year: '2010',\n            title: 'Foundation',\n            description: 'Cast Stone was founded with a vision to revolutionize architectural stone design and manufacturing. Our journey began with a commitment to excellence and innovation in every piece we create.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2012',\n            title: 'First Major Project',\n            description: 'Completed our first large-scale commercial project, establishing our reputation in the industry. This milestone project showcased our capabilities and set the foundation for future growth.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2015',\n            title: 'Innovation Breakthrough',\n            description: 'Developed proprietary casting techniques that enhanced durability and aesthetic appeal. Our research and development team achieved breakthrough innovations that set new industry standards.',\n            image: '/images/CollectionBackground3.jpg'\n        },\n        {\n            year: '2018',\n            title: 'International Expansion',\n            description: 'Expanded operations internationally, bringing our expertise to global markets. We established partnerships worldwide and began serving clients across multiple continents.',\n            image: '/images/catalog-banner-bg.jpg'\n        },\n        {\n            year: '2020',\n            title: 'Sustainable Practices',\n            description: 'Implemented eco-friendly manufacturing processes and sustainable material sourcing. Our commitment to environmental responsibility became a cornerstone of our operations.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2023',\n            title: 'Digital Innovation',\n            description: 'Launched advanced digital design tools and virtual consultation services. We embraced technology to enhance customer experience and streamline our design process.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2024',\n            title: 'Industry Leadership',\n            description: 'Recognized as industry leader with over 500 successful projects worldwide. Our dedication to quality and innovation has established us as the premier choice for architectural stone solutions.',\n            image: '/images/CollectionBackground3.jpg'\n        }\n    ];\n    // Scroll to next section\n    const scrollToTimeline = ()=>{\n        const timelineSection = document.getElementById('timeline-section');\n        if (timelineSection) {\n            timelineSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Navigate timeline\n    const navigateTimeline = (direction)=>{\n        if (direction === 'prev' && selectedYear > 0) {\n            setSelectedYear(selectedYear - 1);\n        } else if (direction === 'next' && selectedYear < timelineData.length - 1) {\n            setSelectedYear(selectedYear + 1);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().storyPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().bannerContainer),\n                    children: [\n                        bannerImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().bannerImage),\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: index === currentImageIndex ? 1 : 0,\n                                    scale: index === currentImageIndex ? 1.05 : 1\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeInOut\"\n                                },\n                                style: {\n                                    backgroundImage: \"url(\".concat(image, \")\"),\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    width: '100%',\n                                    height: '100%',\n                                    backgroundSize: 'cover',\n                                    backgroundPosition: 'center',\n                                    zIndex: index === currentImageIndex ? 1 : 0\n                                }\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTextContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.5\n                                            },\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSubtitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.8\n                                            },\n                                            children: \"In 2010, the world of architectural stone made the discovery of a new brand.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().scrollArrow),\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        delay: 1.2\n                                    },\n                                    onClick: scrollToTimeline,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowIcon),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M7 10L12 15L17 10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowText),\n                                            children: \"Explore Our Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"timeline-section\",\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineBackground),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTitle),\n                                    children: \"TIMELINE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearNavigation),\n                                    children: [\n                                        timelineData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButton), \" \").concat(index === selectedYear ? (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButtonActive) : ''),\n                                                onClick: ()=>setSelectedYear(index),\n                                                children: item.year\n                                            }, item.year, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().moreButton),\n                                            children: [\n                                                \"More\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"16\",\n                                                    height: \"16\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M19 9L12 16L5 9\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContentContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContent),\n                                    initial: {\n                                        opacity: 0,\n                                        x: 50\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImageContainer),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: timelineData[selectedYear].image,\n                                                alt: timelineData[selectedYear].title,\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImage),\n                                                width: 600,\n                                                height: 400,\n                                                style: {\n                                                    objectFit: 'cover'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTextContainer),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineYear),\n                                                    children: timelineData[selectedYear].year\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineItemTitle),\n                                                    children: timelineData[selectedYear].title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineDescription),\n                                                    children: timelineData[selectedYear].description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, selectedYear, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineNavigation),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().prevButton)),\n                                            onClick: ()=>navigateTimeline('prev'),\n                                            disabled: selectedYear === 0,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M15 18L9 12L15 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().nextButton)),\n                                            onClick: ()=>navigateTimeline('next'),\n                                            disabled: selectedYear === timelineData.length - 1,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 18L15 12L9 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionContainer),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem),\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/images/CollectionBackground.jpg\",\n                                        alt: \"Vision of Architectural Excellence\",\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                        width: 600,\n                                        height: 400,\n                                        style: {\n                                            objectFit: 'cover'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                            children: \"A VISION OF ARCHITECTURAL EXCELLENCE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                            children: \"At the age of fifty, Cast Stone decided to create its own brand, with the idea of pushing watchmaking beyond anything that existed at the time, with a new contemporary approach to horology. We was planning to develop one product: the watch of his dreams, an approach that involved operating with little regard for production costs, which were excessive.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                            children: \"When released in 2001, this extraordinary timepiece with its ergonomic tonneau case design punctuated with distinctive torque screws and a compelling six-digit price tag, immediately placed the fledgling brand at the highest summit of the entire luxury watch market.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItemReverse)),\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                            children: \"INNOVATION THROUGH CRAFTSMANSHIP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                            children: \"Our commitment to innovation extends beyond traditional boundaries. Each piece we create represents a perfect fusion of time-honored craftsmanship techniques and cutting-edge technology. This approach allows us to achieve unprecedented levels of precision and aesthetic refinement.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                            children: \"The result is a collection of architectural stone products that not only meet the most demanding technical specifications but also inspire architects and designers to explore new possibilities in their creative endeavors. Every project becomes a testament to our unwavering pursuit of excellence.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/images/CollectionBackground2.jpg\",\n                                        alt: \"Innovation Through Craftsmanship\",\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                        width: 600,\n                                        height: 400,\n                                        style: {\n                                            objectFit: 'cover'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OurStoryPage, \"yIsq3P1r70jr1ywORtZUlvMWrG4=\");\n_c = OurStoryPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OurStoryPage);\nvar _c;\n$RefreshReg$(_c, \"OurStoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/our-story/page.tsx\n"));

/***/ })

});