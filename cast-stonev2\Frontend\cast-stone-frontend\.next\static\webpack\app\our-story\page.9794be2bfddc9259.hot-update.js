"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/our-story/page",{

/***/ "(app-pages-browser)/./src/app/our-story/page.tsx":
/*!************************************!*\
  !*** ./src/app/our-story/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ourStory.module.css */ \"(app-pages-browser)/./src/app/our-story/ourStory.module.css\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OurStoryPage = ()=>{\n    _s();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Banner images array\n    const bannerImages = [\n        '/images/CollectionBackground.jpg',\n        '/images/CollectionBackground2.jpg',\n        '/images/CollectionBackground3.jpg',\n        '/images/catalog-banner-bg.jpg'\n    ];\n    // Auto-rotate banner images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OurStoryPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"OurStoryPage.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"OurStoryPage.useEffect.interval\": (prevIndex)=>(prevIndex + 1) % bannerImages.length\n                    }[\"OurStoryPage.useEffect.interval\"]);\n                }\n            }[\"OurStoryPage.useEffect.interval\"], 5000); // Change image every 5 seconds\n            return ({\n                \"OurStoryPage.useEffect\": ()=>clearInterval(interval)\n            })[\"OurStoryPage.useEffect\"];\n        }\n    }[\"OurStoryPage.useEffect\"], [\n        bannerImages.length\n    ]);\n    // Timeline data with images\n    const timelineData = [\n        {\n            year: '2010',\n            title: 'Foundation',\n            description: 'Cast Stone was founded with a vision to revolutionize architectural stone design and manufacturing. Our journey began with a commitment to excellence and innovation in every piece we create.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2012',\n            title: 'First Major Project',\n            description: 'Completed our first large-scale commercial project, establishing our reputation in the industry. This milestone project showcased our capabilities and set the foundation for future growth.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2015',\n            title: 'Innovation Breakthrough',\n            description: 'Developed proprietary casting techniques that enhanced durability and aesthetic appeal. Our research and development team achieved breakthrough innovations that set new industry standards.',\n            image: '/images/CollectionBackground3.jpg'\n        },\n        {\n            year: '2018',\n            title: 'International Expansion',\n            description: 'Expanded operations internationally, bringing our expertise to global markets. We established partnerships worldwide and began serving clients across multiple continents.',\n            image: '/images/catalog-banner-bg.jpg'\n        },\n        {\n            year: '2020',\n            title: 'Sustainable Practices',\n            description: 'Implemented eco-friendly manufacturing processes and sustainable material sourcing. Our commitment to environmental responsibility became a cornerstone of our operations.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2023',\n            title: 'Digital Innovation',\n            description: 'Launched advanced digital design tools and virtual consultation services. We embraced technology to enhance customer experience and streamline our design process.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2024',\n            title: 'Industry Leadership',\n            description: 'Recognized as industry leader with over 500 successful projects worldwide. Our dedication to quality and innovation has established us as the premier choice for architectural stone solutions.',\n            image: '/images/CollectionBackground3.jpg'\n        }\n    ];\n    // Scroll to next section\n    const scrollToTimeline = ()=>{\n        const timelineSection = document.getElementById('timeline-section');\n        if (timelineSection) {\n            timelineSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Navigate timeline\n    const navigateTimeline = (direction)=>{\n        if (direction === 'prev' && selectedYear > 0) {\n            setSelectedYear(selectedYear - 1);\n        } else if (direction === 'next' && selectedYear < timelineData.length - 1) {\n            setSelectedYear(selectedYear + 1);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().storyPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().bannerContainer),\n                    children: [\n                        bannerImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().bannerImage),\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: index === currentImageIndex ? 1 : 0,\n                                    scale: index === currentImageIndex ? 1.05 : 1\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeInOut\"\n                                },\n                                style: {\n                                    backgroundImage: \"url(\".concat(image, \")\"),\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    width: '100%',\n                                    height: '100%',\n                                    backgroundSize: 'cover',\n                                    backgroundPosition: 'center',\n                                    zIndex: index === currentImageIndex ? 1 : 0\n                                }\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTextContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroTitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.5\n                                            },\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().heroSubtitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.8\n                                            },\n                                            children: \"In 2010, the world of architectural stone made the discovery of a new brand.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().scrollArrow),\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        delay: 1.2\n                                    },\n                                    onClick: scrollToTimeline,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowIcon),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M7 10L12 15L17 10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().arrowText),\n                                            children: \"Explore Our Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"timeline-section\",\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineBackground),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTitle),\n                                    children: \"TIMELINE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearNavigation),\n                                    children: [\n                                        timelineData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButton), \" \").concat(index === selectedYear ? (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().yearButtonActive) : ''),\n                                                onClick: ()=>setSelectedYear(index),\n                                                children: item.year\n                                            }, item.year, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, undefined)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().moreButton),\n                                            children: [\n                                                \"More\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"16\",\n                                                    height: \"16\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M19 9L12 16L5 9\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContentContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineContent),\n                                    initial: {\n                                        opacity: 0,\n                                        x: 50\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImageContainer),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: timelineData[selectedYear].image,\n                                                alt: timelineData[selectedYear].title,\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineImage),\n                                                width: 600,\n                                                height: 400,\n                                                style: {\n                                                    objectFit: 'cover'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineTextContainer),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineYear),\n                                                    children: timelineData[selectedYear].year\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineItemTitle),\n                                                    children: timelineData[selectedYear].title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineDescription),\n                                                    children: timelineData[selectedYear].description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, selectedYear, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().timelineNavigation),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().prevButton)),\n                                            onClick: ()=>navigateTimeline('prev'),\n                                            disabled: selectedYear === 0,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M15 18L9 12L15 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().nextButton)),\n                                            onClick: ()=>navigateTimeline('next'),\n                                            disabled: selectedYear === timelineData.length - 1,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9 18L15 12L9 6\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().visionContainer),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem),\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/images/CollectionBackground.jpg\",\n                                        alt: \"Vision of Architectural Excellence\",\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                        width: 600,\n                                        height: 400,\n                                        style: {\n                                            objectFit: 'cover'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                            children: \"A VISION OF ARCHITECTURAL EXCELLENCE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                            children: \"At the age of fifty, Cast Stone decided to create its own brand, with the idea of pushing watchmaking beyond anything that existed at the time, with a new contemporary approach to horology. We was planning to develop one product: the watch of his dreams, an approach that involved operating with little regard for production costs, which were excessive.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                            children: \"When released in 2001, this extraordinary timepiece with its ergonomic tonneau case design punctuated with distinctive torque screws and a compelling six-digit price tag, immediately placed the fledgling brand at the highest summit of the entire luxury watch market.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"\".concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItem), \" \").concat((_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogItemReverse)),\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogContent),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogTitle),\n                                            children: \"INNOVATION THROUGH CRAFTSMANSHIP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                            children: \"Our commitment to innovation extends beyond traditional boundaries. Each piece we create represents a perfect fusion of time-honored craftsmanship techniques and cutting-edge technology. This approach allows us to achieve unprecedented levels of precision and aesthetic refinement.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogText),\n                                            children: \"The result is a collection of architectural stone products that not only meet the most demanding technical specifications but also inspire architects and designers to explore new possibilities in their creative endeavors. Every project becomes a testament to our unwavering pursuit of excellence.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImageContainer),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/images/CollectionBackground2.jpg\",\n                                        alt: \"Innovation Through Craftsmanship\",\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().blogImage),\n                                        width: 600,\n                                        height: 400,\n                                        style: {\n                                            objectFit: 'cover'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteSection),\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteContainer),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quote),\n                                                children: '\"For a long time, I wished to launch my own brand. I wanted to create a new business model, far removed from traditional marketing strategies, something totally original. My goal was to create a new, ultra-high end luxury segment within the high end watch business, and I was very eager to know what could come of it!\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"cite\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteAuthor),\n                                                children: \"RICHARD MILLE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteTextContent),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteTitle),\n                                                children: \"THE FRUITION OF DECADES OF EXPERIENCE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_3___default().quoteText),\n                                                children: \"For Richard Mille, this was not an impulsive decision quickly taken; it was the direct fruition of decades of experience gained by existing diverse watchmaking and elite jewelry brands in creation, management and development of a number of different products. His deep fascination for technology, his expertise and personal passion for racing cars combined with his extreme sensitivity to design and ergonomics, meant that no watch in existence at the time could completely meet his expectations.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OurStoryPage, \"yIsq3P1r70jr1ywORtZUlvMWrG4=\");\n_c = OurStoryPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OurStoryPage);\nvar _c;\n$RefreshReg$(_c, \"OurStoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/our-story/page.tsx\n"));

/***/ })

});