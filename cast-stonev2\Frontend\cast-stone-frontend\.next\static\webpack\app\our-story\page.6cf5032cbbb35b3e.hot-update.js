"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/our-story/page",{

/***/ "(app-pages-browser)/./src/app/our-story/page.tsx":
/*!************************************!*\
  !*** ./src/app/our-story/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ourStory.module.css */ \"(app-pages-browser)/./src/app/our-story/ourStory.module.css\");\n/* harmony import */ var _ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst OurStoryPage = ()=>{\n    _s();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Banner images array\n    const bannerImages = [\n        '/images/CollectionBackground.jpg',\n        '/images/CollectionBackground2.jpg',\n        '/images/CollectionBackground3.jpg',\n        '/images/catalog-banner-bg.jpg'\n    ];\n    // Auto-rotate banner images\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OurStoryPage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"OurStoryPage.useEffect.interval\": ()=>{\n                    setCurrentImageIndex({\n                        \"OurStoryPage.useEffect.interval\": (prevIndex)=>(prevIndex + 1) % bannerImages.length\n                    }[\"OurStoryPage.useEffect.interval\"]);\n                }\n            }[\"OurStoryPage.useEffect.interval\"], 5000); // Change image every 5 seconds\n            return ({\n                \"OurStoryPage.useEffect\": ()=>clearInterval(interval)\n            })[\"OurStoryPage.useEffect\"];\n        }\n    }[\"OurStoryPage.useEffect\"], [\n        bannerImages.length\n    ]);\n    // Timeline data with images\n    const timelineData = [\n        {\n            year: '2010',\n            title: 'Foundation',\n            description: 'Cast Stone was founded with a vision to revolutionize architectural stone design and manufacturing. Our journey began with a commitment to excellence and innovation in every piece we create.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2012',\n            title: 'First Major Project',\n            description: 'Completed our first large-scale commercial project, establishing our reputation in the industry. This milestone project showcased our capabilities and set the foundation for future growth.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2015',\n            title: 'Innovation Breakthrough',\n            description: 'Developed proprietary casting techniques that enhanced durability and aesthetic appeal. Our research and development team achieved breakthrough innovations that set new industry standards.',\n            image: '/images/CollectionBackground3.jpg'\n        },\n        {\n            year: '2018',\n            title: 'International Expansion',\n            description: 'Expanded operations internationally, bringing our expertise to global markets. We established partnerships worldwide and began serving clients across multiple continents.',\n            image: '/images/catalog-banner-bg.jpg'\n        },\n        {\n            year: '2020',\n            title: 'Sustainable Practices',\n            description: 'Implemented eco-friendly manufacturing processes and sustainable material sourcing. Our commitment to environmental responsibility became a cornerstone of our operations.',\n            image: '/images/CollectionBackground.jpg'\n        },\n        {\n            year: '2023',\n            title: 'Digital Innovation',\n            description: 'Launched advanced digital design tools and virtual consultation services. We embraced technology to enhance customer experience and streamline our design process.',\n            image: '/images/CollectionBackground2.jpg'\n        },\n        {\n            year: '2024',\n            title: 'Industry Leadership',\n            description: 'Recognized as industry leader with over 500 successful projects worldwide. Our dedication to quality and innovation has established us as the premier choice for architectural stone solutions.',\n            image: '/images/CollectionBackground3.jpg'\n        }\n    ];\n    // Scroll to next section\n    const scrollToTimeline = ()=>{\n        const timelineSection = document.getElementById('timeline-section');\n        if (timelineSection) {\n            timelineSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    // Navigate timeline\n    const navigateTimeline = (direction)=>{\n        if (direction === 'prev' && selectedYear > 0) {\n            setSelectedYear(selectedYear - 1);\n        } else if (direction === 'next' && selectedYear < timelineData.length - 1) {\n            setSelectedYear(selectedYear + 1);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().storyPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().bannerContainer),\n                    children: [\n                        bannerImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().bannerImage),\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: index === currentImageIndex ? 1 : 0,\n                                    scale: index === currentImageIndex ? 1.05 : 1\n                                },\n                                transition: {\n                                    duration: 1.5,\n                                    ease: \"easeInOut\"\n                                },\n                                style: {\n                                    backgroundImage: \"url(\".concat(image, \")\"),\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    width: '100%',\n                                    height: '100%',\n                                    backgroundSize: 'cover',\n                                    backgroundPosition: 'center',\n                                    zIndex: index === currentImageIndex ? 1 : 0\n                                }\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroTextContainer),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroTitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 50\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.5\n                                            },\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().heroSubtitle),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 1,\n                                                delay: 0.8\n                                            },\n                                            children: \"In 2010, the world of architectural stone made the discovery of a new brand.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().scrollArrow),\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 1,\n                                        delay: 1.2\n                                    },\n                                    onClick: scrollToTimeline,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().arrowIcon),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M7 10L12 15L17 10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().arrowText),\n                                            children: \"Explore Our Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().breadcrumbSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().container),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().breadcrumb),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"The Company\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"•\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().currentPage),\n                                children: \"Our Story\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().mainContent),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().container),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineSection),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().sectionHeader),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().sectionTitle),\n                                        children: \"Our Journey\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineContainer),\n                                    children: timelineData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineItem),\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineYear),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.year\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineDot)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineContent),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineTitle),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().timelineDescription),\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, item.year, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionContent),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionTitle),\n                                        children: \"A vision of architectural excellence\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionText),\n                                        children: \"At Cast Stone, we believe that exceptional architecture begins with exceptional materials. Our journey started with a simple yet profound vision: to create cast stone products that not only meet the highest standards of quality and durability but also inspire architects and designers to push the boundaries of what's possible.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().visionText),\n                                        children: \"From our humble beginnings to becoming an industry leader, we have remained committed to innovation, craftsmanship, and sustainability. Every piece we create tells a story of dedication, precision, and artistic vision.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseContent),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseTitle),\n                                        children: \"The fruition of decades of experience\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseText),\n                                        children: \"Our expertise in cast stone manufacturing represents the culmination of years of research, development, and hands-on experience. We have mastered the art of combining traditional craftsmanship with cutting-edge technology to create products that stand the test of time.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().expertiseText),\n                                        children: \"This deep understanding of materials, combined with our passion for architectural innovation and our commitment to excellence, means that every Cast Stone product exceeds expectations in both form and function.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipContent),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipTitle),\n                                        children: \"Building lasting partnerships\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipText),\n                                        children: \"Our success is built on strong partnerships with architects, designers, contractors, and clients who share our vision for excellence. We work closely with each partner to understand their unique requirements and deliver solutions that exceed expectations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().partnershipText),\n                                        children: \"These collaborative relationships have been the foundation of our growth and continue to drive innovation in everything we do. Together, we create architectural masterpieces that define skylines and inspire communities.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().successSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().successContent),\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().successTitle),\n                                        children: \"A successful formula\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_ourStory_module_css__WEBPACK_IMPORTED_MODULE_2___default().successText),\n                                        children: \"Those who understand the uncompromising quality of our visionary products have made Cast Stone an unequivocal success. Today, over a decade later, our product portfolio comprises more than 200 unique designs, each crafted with the same passion and uncompromising principles that guided our first creation.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\our-story\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OurStoryPage, \"yIsq3P1r70jr1ywORtZUlvMWrG4=\");\n_c = OurStoryPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OurStoryPage);\nvar _c;\n$RefreshReg$(_c, \"OurStoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/our-story/page.tsx\n"));

/***/ })

});