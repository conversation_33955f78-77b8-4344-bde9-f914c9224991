/* Product Specifications - Sharp Rectangular Design */
.specificationsContainer {
  /* margin: 3rem 0; */
  /* margin: 2rem 0 0 0 auto;
  width: 100%;
  max-width: 40vw; */

  margin-top: 2rem;
  /* margin-left: auto; */
  width: 100%;
  /* max-width: 35vw; */
  /* margin-top: -20rem; */
  margin-bottom: 15rem;
}

/* Section Styles */
.section {
  border: 0px solid #ddd;
  border-radius: 0; /* Sharp corners */
  margin-bottom: 1rem;
  background: white;
}

.sectionHeader {
  width: 100%;
  background: #f5f5f5;
  border: none;
  border-bottom: 1px solid #ddd;
  padding: 1.5rem;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color:rgb(14, 14, 14);
  transition: background-color 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.sectionHeader:hover {
  background: #eeeeee;
}

.sectionHeader.active {
    /* background: #f5f5f5; */
  background: #f5f5f5;
  color: black;
}

.toggleIcon {
  font-size: 1.5rem;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.sectionContent {
  padding: 2rem;
  border-top: 1px solid #ddd;
}

/* Specifications Grid - Simple Lines Style */
.specGrid {
  display: block;
  width: 100%;
  background: white;
  margin-top: 1rem;
}
.keySpecsTable {
  width: 100%;
  max-width: 600px;
  font-family: "Arial", sans-serif;
  font-size: 15px;
  color: #111;
  border-collapse: collapse;
  margin-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

/* .specRow {
  display: flex;
  align-items: baseline;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.specRow:last-child {
  border-bottom: none;
}

.specLabel {
  font-weight: 600;
  color: #1f2937;
  font-size: 1rem;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.specValue {
  color: #1f2937;
  font-size: 1rem;
  flex: 1;
  margin-left: 1rem;
  text-align: left;
} */

.specRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 6px 0;
  border-bottom: 1px solid #d1d5db; /* Tailwind gray-300 */
}

.specRow:last-child {
  border-bottom: none;
}

.specLabel {
  width: 50%;
  font-weight: 600;
  font-size: 0.875rem; /* text-sm */
  color: #111827; /* Tailwind gray-900 */
  line-height: 1.5;
}

.specValue {
  width: 50%;
  font-size: 0.875rem;
  color: #111827;
  text-align: left;
  line-height: 1.5;
}


.specValue.inStock {
  color: #28a745;
  font-weight: 600;
}

.specValue.outOfStock {
  color: #dc3545;
  font-weight: 600;
}

/* Tags */
.tagContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background: #1e40af;
  color: white;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  border-radius: 0; /* Sharp corners */
  border: 1px solid #1e40af;
}

/* Details Content */
.detailsContent {
  line-height: 1.6;
}

.description {
  color: #555;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.featureList h4 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.featureList ul {
  list-style: none;
  padding: 0;
}

.featureList li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 1.5rem;
}

.featureList li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #1e40af;
  font-weight: bold;
}

.featureList li:last-child {
  border-bottom: none;
}

/* Care Content */
.careContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.careSection h4,
.downloadSection h4 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.careSection ul {
  list-style: none;
  padding: 0;
}

.careSection li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  padding-left: 1.5rem;
}

.careSection li:before {
  content: '•';
  position: absolute;
  left: 0;
  color: #1e40af;
  font-weight: bold;
}

.careSection li:last-child {
  border-bottom: none;
}

/* Download Links */
.downloadLinks {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.downloadLink {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid #1e40af;
  color: #1e40af;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
  font-weight: 500;
}

.downloadLink:hover {
  background: #1e40af;
  color: white;
}

/* Share Section */
.shareSection {
  margin-top: 2rem;
  padding: 1.5rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  background: #f8f8f8;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.shareLabel {
  font-weight: 600;
  color: #1e40af;
  font-size: 1rem;
}

.shareButtons {
  display: flex;
  gap: 0.75rem;
}

.shareButton {
  background: #1e40af;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.shareButton:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .careContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}


  @media (max-width: 768px) {
      
  .shareSection {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .shareButtons {
    justify-content: center;
  }  

  .downloadLink {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
  
  .shareButton {
    width: 35px;
    height: 35px;
  }
  
  .tagContainer {
    gap: 0.25rem;
  }
  
  .tag {
    font-size: 0.75rem;
    padding: 0.2rem 0.5rem;
  }
  .specRow {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between;
    align-items: flex-start;
     /* align-items: baseline; */
    gap: 0.5rem;
    grid-template-columns: 1fr;
    flex-wrap: wrap;
    padding: 0.4rem 0;
    border-bottom: 1px solid #e5e7eb;
    line-height: 1.3;
  }

  .specLabel {
    font-weight: 600;
    flex: 0 0 auto;
    min-width: 100px;
    font-size: 0.9rem;
  }

  .specGrid {
    display: block;
    margin-top: 0.5rem;
  }


  .specRow:last-child {
    border-bottom: none;
  }

  .specLabel {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
    margin-right: 0.4rem;
    flex-shrink: 0;
  }

  .specValue {
    color: #1f2937;
    font-size: 0.9rem;
    flex: 1;
    word-break: break-word;
  }

  .sectionHeader {
    padding: 1rem;
    font-size: 1rem;
  }

  .sectionContent {
    padding: 1rem;
  }

}


