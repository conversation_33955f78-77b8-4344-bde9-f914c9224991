/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/Home/HeroSection/heroSection.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/* Hero Section Styles */
.heroSection_hero__HFsef {
  position: relative;
  height: 100vh;
  min-height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #1a1a1a; /* Fallback color */
}

/* Image Background Carousel */
.heroSection_imageContainer__H2IAa {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroSection_imageSlide__Fk2HZ {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1.5s ease-in-out;
  transform: scale(1);
  will-change: transform, opacity;
}

.heroSection_imageSlide__Fk2HZ.heroSection_active__iz5al {
  opacity: 1;
  animation: heroSection_smoothZoom__NU0_y 5s ease-out forwards;
}

.heroSection_imageSlide__Fk2HZ:not(.heroSection_active__iz5al) {
  animation: none;
  transform: scale(1.06); /* Keep zoomed state when inactive */
}

/* Smooth zoom animation without reset */
@keyframes heroSection_smoothZoom__NU0_y {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.06);
  }
}

.heroSection_backgroundImage__UAIPT {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
}

.heroSection_imageOverlay__8xgBd {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: 2;
}

/* Content Styles */
.heroSection_container__VMTSp {
  position: relative;
  z-index: 3;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.heroSection_content__edId8 {
  max-width: 900px;
  margin: 0 auto;
  padding-top: 2rem;
}

.heroSection_title__I5_2D {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.1;
  margin-bottom: 2rem;
  color: #ffffff;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.02em;
}

.heroSection_titleLine1__jGTDk,
.heroSection_titleLine2__inUap {
  display: block;
  animation: heroSection_fadeInUp__CUO1t 1s ease-out forwards;
}

.heroSection_titleLine1__jGTDk {
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateY(30px);
}

.heroSection_titleLine2__inUap {
  animation-delay: 0.6s;
  opacity: 0;
  transform: translateY(30px);
}

.heroSection_subtitle__mxjNP {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.3rem;
  font-weight: 300;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
  animation: heroSection_fadeInUp__CUO1t 1s ease-out 0.9s forwards;
  opacity: 0;
  transform: translateY(30px);
}

/* Button Styles */
.heroSection_actions__a9kqx {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  animation: heroSection_fadeInUp__CUO1t 1s ease-out 1.2s forwards;
  opacity: 0;
  transform: translateY(30px);
}

.heroSection_primaryButton__72OT1,
.heroSection_secondaryButton__EGl_p {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1.2rem 3rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.15em;
  text-transform: uppercase;
  text-decoration: none;
  border-radius: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  min-width: 220px;
}

.heroSection_primaryButton__72OT1 {
  background: rgba(255, 255, 255, 0.9);
  color: rgba(0, 0, 0, 0.8);
  border-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.heroSection_primaryButton__72OT1:hover {
  background: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.heroSection_secondaryButton__EGl_p {
  background: transparent;
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.heroSection_secondaryButton__EGl_p:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 1);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
}

.heroSection_buttonText__1grMJ {
  position: relative;
  z-index: 2;
}

.heroSection_buttonRipple__mt_w3 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: scale(0);
  transition: transform 0.6s ease-out;
  z-index: 1;
}

.heroSection_primaryButton__72OT1:active .heroSection_buttonRipple__mt_w3,
.heroSection_secondaryButton__EGl_p:active .heroSection_buttonRipple__mt_w3 {
  transform: scale(1);
}

/* Scroll Indicator */
.heroSection_scrollIndicator__7SDx5 {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  animation: heroSection_fadeInUp__CUO1t 1s ease-out 1.5s forwards;
  opacity: 0;
}

.heroSection_scrollArrow__v7b7W {
  color: #ffffff;
  animation: heroSection_bounce__YOATy 2s infinite;
  cursor: pointer;
  transition: color 0.3s ease;
}

.heroSection_scrollArrow__v7b7W:hover {
  color: #d4af8c;
}

/* Animations */
@keyframes heroSection_fadeInUp__CUO1t {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes heroSection_bounce__YOATy {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .heroSection_title__I5_2D {
    font-size: 3.5rem;
  }

  .heroSection_subtitle__mxjNP {
    font-size: 1.1rem;
  }

  .heroSection_container__VMTSp {
    padding: 0 1.5rem;
  }
}

@media (max-width: 768px) {
  .heroSection_title__I5_2D {
    font-size: 2.5rem;
  }

  .heroSection_subtitle__mxjNP {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .heroSection_actions__a9kqx {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .heroSection_primaryButton__72OT1,
  .heroSection_secondaryButton__EGl_p {
    width: 100%;
    max-width: 280px;
    padding: 0.875rem 2rem;
  }

  .heroSection_container__VMTSp {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .heroSection_hero__HFsef {
    min-height: 500px;
  }

  .heroSection_title__I5_2D {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .heroSection_subtitle__mxjNP {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .heroSection_primaryButton__72OT1,
  .heroSection_secondaryButton__EGl_p {
    font-size: 0.8rem;
    padding: 0.75rem 1.5rem;
  }
}

/* Navigation Arrows */
.heroSection_navArrow__Iv0Fd {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  /* background: rgba(255, 255, 255, 0.1); */
  /* backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 0%; */
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 4;
  opacity: 0.7;
}

.heroSection_navArrow__Iv0Fd:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
  color: rgba(255, 255, 255, 1);
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

.heroSection_navArrowLeft__u8vNu {
  left: 2rem;
}

.heroSection_navArrowRight__H0tWR {
  right: 2rem;
}

/* Indicator Dots */
.heroSection_indicators__63sjh {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 3rem;
  padding: 1rem 0;
}

.heroSection_indicator__oBwwq {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.heroSection_indicator__oBwwq:hover {
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.heroSection_indicatorActive__f6qRm {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .heroSection_hero__HFsef {
    min-height: 500px;
  }

  .heroSection_navArrow__Iv0Fd {
    width: 50px;
    height: 50px;
    opacity: 0.6;
  }

  .heroSection_navArrowLeft__u8vNu {
    left: 1rem;
  }

  .heroSection_navArrowRight__H0tWR {
    right: 1rem;
  }

  .heroSection_indicators__63sjh {
    margin-top: 2rem;
    gap: 0.5rem;
  }

  .heroSection_indicator__oBwwq {
    width: 10px;
    height: 10px;
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/Home/CategoriesSection/categoriesSection.module.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Categories Section Styles */
.categoriesSection_categoriesSection__L5ajJ {
  padding: 6rem 0;
  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);
  position: relative;
}

.categoriesSection_container__Juhf1 {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  /* height: 100% */
}

/* Header Styles */
.categoriesSection_header__sEQPh {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.categoriesSection_sectionTitle__71v8k {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.categoriesSection_sectionSubtitle__C9a1h {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  color:rgb(54, 76, 137);
  line-height: 1.6;
  font-weight: 400;
}

/* Grid Layout */
.categoriesSection_grid__Z5DfW {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Loading States */
.categoriesSection_loadingGrid__chWe_ {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.categoriesSection_loadingCard__sZ2aU {
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.08);
  animation: categoriesSection_pulse__t9Vlx 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.categoriesSection_loadingImage__0EmAx {
  width: 100%;
  height: 300px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: categoriesSection_shimmer__1P_X4 2s infinite;
}

.categoriesSection_loadingContent___HlWp {
  padding: 2rem;
}

.categoriesSection_loadingText__vF9G2 {
  height: 1rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: categoriesSection_shimmer__1P_X4 2s infinite;
  border-radius: 4px;
  margin-bottom: 0.75rem;
}

.categoriesSection_loadingText__vF9G2:nth-child(1) {
  width: 60%;
}

.categoriesSection_loadingText__vF9G2:nth-child(2) {
  width: 80%;
}

.categoriesSection_loadingText__vF9G2:nth-child(3) {
  width: 40%;
}

@keyframes categoriesSection_shimmer__1P_X4 {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes categoriesSection_pulse__t9Vlx {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Error State */
.categoriesSection_errorMessage__lmeIO {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
  font-size: 1.125rem;
}

/* Category Card Styles */
.categoriesSection_categoryCard__hBqob {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 8px 32px rgba(37, 99, 235, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 600px;
}

.categoriesSection_categoryCard__hBqob:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(37, 99, 235, 0.15);
}

.categoriesSection_cardLink__PrqVA {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: inherit;
  position: relative;
}

/* Image Styles */
.categoriesSection_imageContainer__viZU7 {
  position: relative;
  height: 60%;
  overflow: hidden;
}

.categoriesSection_categoryImage__Vg8b_ {
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.categoriesSection_categoryCard__hBqob:hover .categoriesSection_categoryImage__Vg8b_ {
  transform: scale(1.05);
}

.categoriesSection_imageOverlay__NK8SN {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
rgba(25, 35, 65, 0.4) 0%,
    rgba(20, 30, 60, 0.35) 50%,
    rgba(30, 40, 70, 0.4) 100%
  );
  z-index: 1;
}

.categoriesSection_noImagePlaceholder___9GrL {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.categoriesSection_noImageText__p24V9 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Card Content */
.categoriesSection_cardContent__Rly4j {
  position: relative;
  height: 40%;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
}

.categoriesSection_cardHeader__wqden {
  margin-bottom: 1rem;
}

.categoriesSection_stats__okqNF {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.categoriesSection_statsNumber__Xr_Bv {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2rem;
  font-weight: 700;
  color: #1e3a8a;
  line-height: 1;
}

.categoriesSection_statsLabel__q_kTB {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.75rem;
  font-weight: 500;
  color: #1e3a8a;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.categoriesSection_categoryTitle__2ZPRJ {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e3a8a;
  margin: 0.25rem 0;
  line-height: 1.2;
}

.categoriesSection_categorySubtitle__VGk_A {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.8rem;
  font-weight: 600;
  color:rgb(48, 69, 126);
  text-transform: uppercase;
  letter-spacing: 0.15em;
  margin: 0;
}

.categoriesSection_categoryDescription__s91pu {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  color: #1e3a8a;
  line-height: 1.5;
  margin-bottom: 1rem;
  flex-grow: 1;
}

/* Card Actions */
.categoriesSection_cardActions__gIcZA {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.categoriesSection_actionButton__SwruZ,
.categoriesSection_secondaryButton__kh1AY {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.categoriesSection_actionButton__SwruZ {
  background: #1e3a8a;
  color: #ffffff;
  padding: 0.5rem 1rem;
}

.categoriesSection_actionButton__SwruZ:hover {
  background: #1e3a8a;
  transform: translateY(-1px);
}

.categoriesSection_secondaryButton__kh1AY {
  background: transparent;
  color:rgb(51, 73, 134);
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(105, 120, 161, 0.3);
}

.categoriesSection_secondaryButton__kh1AY:hover {
  background: rgba(69, 66, 153, 0.1);
  color: #1e3a8a;
}

/* Hover Effect */
.categoriesSection_hoverEffect__wVWL3 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(37, 99, 235, 0.05) 0%,
    transparent 50%,
    rgba(29, 78, 216, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.categoriesSection_categoryCard__hBqob:hover .categoriesSection_hoverEffect__wVWL3 {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .categoriesSection_categoriesSection__L5ajJ {
    padding: 4rem 0;
  }
  
  .categoriesSection_container__Juhf1 {
    padding: 0 1.5rem;
  }
  
  .categoriesSection_sectionTitle__71v8k {
    font-size: 2.5rem;
  }
  
  .categoriesSection_grid__Z5DfW {
    gap: 1.5rem;
  }
  
  .categoriesSection_categoryCard__hBqob {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .categoriesSection_grid__Z5DfW {
    grid-template-columns: 1fr;
    gap: 2rem; /* Increased gap between cards */
  }

  .categoriesSection_categoryCard__hBqob {
    height: 380px; /* Increased height for better mobile experience */
  }

  .categoriesSection_sectionTitle__71v8k {
    font-size: 2rem;
  }

  .categoriesSection_sectionSubtitle__C9a1h {
    font-size: 1rem;
  }

  .categoriesSection_cardContent__Rly4j {
    padding: 1.25rem;
  }

  .categoriesSection_statsNumber__Xr_Bv {
    font-size: 1.75rem;
  }

  .categoriesSection_categoryTitle__2ZPRJ {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .categoriesSection_categoriesSection__L5ajJ {
    padding: 3rem 0;
  }

  .categoriesSection_container__Juhf1 {
    padding: 0 1rem;
  }

  .categoriesSection_header__sEQPh {
    margin-bottom: 2.5rem;
  }

  .categoriesSection_grid__Z5DfW {
    gap: 1.5rem; /* Better gap for small screens */
  }

  .categoriesSection_categoryCard__hBqob {
    height: 360px; /* Increased height for better mobile experience */
  }

  .categoriesSection_cardContent__Rly4j {
    padding: 1rem;
  }

  .categoriesSection_cardActions__gIcZA {
    flex-direction: column;
    gap: 0.5rem;
  }

  .categoriesSection_actionButton__SwruZ,
  .categoriesSection_secondaryButton__kh1AY {
    width: 100%;
    justify-content: center;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/Home/CatalogBanner/catalogBanner.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/* Catalog Banner Styles */
.catalogBanner_catalogBanner__wBZRQ {
  position: relative;
  padding: 8rem 0;
  background: #1e3a8a;
  overflow: hidden;
  margin: 4rem 0;
}

/* Background Styles */
.catalogBanner_backgroundContainer__ZMmEK {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.catalogBanner_backgroundImage__3auvx {
  object-fit: cover;
  object-position: center;
}

.catalogBanner_backgroundOverlay__NaZBC {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
rgba(25, 35, 65, 0.4) 0%,
    rgba(20, 30, 60, 0.35) 50%,
    rgba(30, 40, 70, 0.4) 100%
  );
  z-index: 2;
}

/* Content Styles */
.catalogBanner_container__NpNqR {
  position: relative;
  z-index: 3;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.catalogBanner_content__Rp_xy {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.catalogBanner_textContent__cSOWs {
  color: #ffffff;
}

.catalogBanner_subtitle__se9H5 {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  color: #white;
  margin-bottom: 1rem;
  display: block;
}

.catalogBanner_title__7S2Hu {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  color: #ffffff;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.catalogBanner_description___U0Ow {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
  font-weight: 400;
}

/* Features */
.catalogBanner_features__wz4Ik {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.catalogBanner_feature__hLAGD {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.catalogBanner_featureIcon__wduQi {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(212, 175, 140, 0.2);
  border-radius: 50%;
  color: #white;
  flex-shrink: 0;
}

/* CTA Section */
.catalogBanner_ctaContainer__for5y {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2rem;
}

.catalogBanner_ctaButton__S68Qj {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 2.5rem;
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  color: #ffffff;
  text-decoration: none;
  border-radius: 50px;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(37, 99, 235, 0.3);
}

.catalogBanner_ctaButton__S68Qj:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(37, 99, 235, 0.4);
  background: linear-gradient(135deg, #1d4ed8, #2563eb);
}

.catalogBanner_ctaText__k_Cqp {
  position: relative;
  z-index: 2;
}

.catalogBanner_ctaIcon__F6X4F {
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.catalogBanner_ctaButton__S68Qj:hover .catalogBanner_ctaIcon__F6X4F {
  transform: translateX(4px);
}

.catalogBanner_buttonRipple__QAuT1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: scale(0);
  transition: transform 0.6s ease-out;
  z-index: 1;
}

.catalogBanner_ctaButton__S68Qj:active .catalogBanner_buttonRipple__QAuT1 {
  transform: scale(1);
}

/* Catalog Stats */
.catalogBanner_catalogStats__U_mTo {
  display: flex;
  gap: 3rem;
}

.catalogBanner_stat__8h24Z {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.catalogBanner_statNumber__ygMfW {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #white;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.catalogBanner_statLabel__In1Ia {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Decorative Elements */
.catalogBanner_decorativeElements__wjETD {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}

.catalogBanner_decorativeCircle__VGQfY {
  position: absolute;
  top: 20%;
  right: 10%;
  width: 200px;
  height: 200px;
  border: 2px solid rgba(212, 175, 140, 0.2);
  border-radius: 50%;
  animation: catalogBanner_float___rFbY 6s ease-in-out infinite;
}

.catalogBanner_decorativeLine__b5Od1 {
  position: absolute;
  bottom: 20%;
  left: 5%;
  width: 150px;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 140, 0.3), transparent);
  animation: catalogBanner_slide__J0rbu 8s ease-in-out infinite;
}

/* Animations */
@keyframes catalogBanner_float___rFbY {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes catalogBanner_slide__J0rbu {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(50px);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .catalogBanner_catalogBanner__wBZRQ {
    padding: 6rem 0;
  }
  
  .catalogBanner_content__Rp_xy {
    gap: 3rem;
  }
  
  .catalogBanner_title__7S2Hu {
    font-size: 3rem;
  }
  
  .catalogBanner_features__wz4Ik {
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .catalogBanner_catalogBanner__wBZRQ {
    padding: 4rem 0;
    margin: 3rem 0;
  }
  
  .catalogBanner_container__NpNqR {
    padding: 0 1.5rem;
  }
  
  .catalogBanner_content__Rp_xy {
    grid-template-columns: 1fr;
    gap: 2.5rem;
    text-align: center;
  }
  
  .catalogBanner_title__7S2Hu {
    font-size: 2.5rem;
  }
  
  .catalogBanner_description___U0Ow {
    font-size: 1rem;
  }
  
  .catalogBanner_features__wz4Ik {
    justify-content: center;
    gap: 1rem;
  }
  
  .catalogBanner_ctaContainer__for5y {
    align-items: center;
  }
  
  .catalogBanner_catalogStats__U_mTo {
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .catalogBanner_catalogBanner__wBZRQ {
    padding: 3rem 0;
  }
  
  .catalogBanner_container__NpNqR {
    padding: 0 1rem;
  }
  
  .catalogBanner_title__7S2Hu {
    font-size: 2rem;
  }
  
  .catalogBanner_features__wz4Ik {
    flex-direction: column;
    gap: 1rem;
  }
  
  .catalogBanner_feature__hLAGD {
    justify-content: center;
  }
  
  .catalogBanner_ctaButton__S68Qj {
    padding: 1rem 2rem;
    font-size: 0.9rem;
  }
  
  .catalogBanner_catalogStats__U_mTo {
    gap: 1.5rem;
  }
  
  .catalogBanner_statNumber__ygMfW {
    font-size: 2rem;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/Home/CollectionsCarousel/collectionsCarousel.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Collections Carousel Styles */
.collectionsCarousel_collectionsSection__PL2_N {
  padding: 6rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #faf9f7 100%);
}

.collectionsCarousel_container__ojJCz {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Header Styles */
.collectionsCarousel_header__v5IST {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 3rem;
  gap: 2rem;
}

.collectionsCarousel_headerContent__95MOK {
  flex: 1;
}

.collectionsCarousel_title__WhpBj {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3rem;
  font-weight: 700;
  color: #1e3a8a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.collectionsCarousel_subtitle__78z9Y {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  color: #1e40af;
  line-height: 1.6;
  font-weight: 400;
  max-width: 600px;
}

/* Navigation */
.collectionsCarousel_navigation__61e2M {
  display: flex;
  gap: 0.5rem;
}

.collectionsCarousel_navButton__2RMsy {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #ffffff;
  border: 2px solid rgba(37, 99, 235, 0.1);
  border-radius: 50%;
  color: #1e3a8a;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);
}

.collectionsCarousel_navButton__2RMsy:hover {
  background: #1e3a8a;
  color: #ffffff;
  border-color: #1e3a8a;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.2);
}

/* Carousel Container */
.collectionsCarousel_carouselContainer__szE7j {
  position: relative;
  margin-bottom: 3rem;
}

.collectionsCarousel_carousel__qCGVG {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  scroll-behavior: smooth;
  padding: 1rem 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.collectionsCarousel_carousel__qCGVG::-webkit-scrollbar {
  display: none;
}

/* Collection Card */
.collectionsCarousel_collectionCard__cNhDx {
  flex: 0 0 320px;
  height: 400px;
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 8px 32px rgba(30, 11, 172, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.collectionsCarousel_collectionCard__cNhDx:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(37, 99, 235, 0.15);
}

.collectionsCarousel_cardLink__FkgHa {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

/* Image Styles */
.collectionsCarousel_imageContainer__l8dza {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.collectionsCarousel_collectionImage__sbJui {
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.collectionsCarousel_collectionCard__cNhDx:hover .collectionsCarousel_collectionImage__sbJui {
  transform: scale(1.05);
}

.collectionsCarousel_imageOverlay__IJNTi {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
rgba(25, 35, 65, 0.4) 0%,
    rgba(20, 30, 60, 0.35) 50%,
    rgba(30, 40, 70, 0.4) 100%
  );
  z-index: 1;
}

.collectionsCarousel_noImagePlaceholder__ss9Fk {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.collectionsCarousel_noImageText__BtdW5 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Card Content */
.collectionsCarousel_cardContent__T3Io_ {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  color: #ffffff;
  z-index: 2;
  background: linear-gradient(
    to top,
rgba(25, 35, 65, 0.4) 0%,
    rgba(20, 30, 60, 0.35) 50%,
    rgba(30, 40, 70, 0.4) 100%
    transparent 100%
  );
}

.collectionsCarousel_productCount__upDHt {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: #1e3a8a;
  margin-bottom: 0.5rem;
}

.collectionsCarousel_collectionName__tYse8 {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.75rem;
  line-height: 1.2;
}

.collectionsCarousel_collectionDescription__VMKY1 {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.collectionsCarousel_cardAction__bPcQm {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #ffffffff;
  transition: all 0.3s ease;
}

.collectionsCarousel_collectionCard__cNhDx:hover .collectionsCarousel_cardAction__bPcQm {
  color: #ffffff;
  transform: translateX(4px);
}

.collectionsCarousel_actionText__OvOrT {
  color: #ffffff;
  transition: transform 0.3s ease;
}

/* View All Button */
.collectionsCarousel_viewAllContainer__gfXZ2 {
  text-align: center;
}

.collectionsCarousel_viewAllButton__tnjpy {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: transparent;
  color: #1e3a8a;
  text-decoration: none;
  border: 2px solid #1e3a8a;
  border-radius: 50px;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.collectionsCarousel_viewAllButton__tnjpy:hover {
  background: #1e3a8a;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* Loading and Error States */
.collectionsCarousel_loadingContainer__6bZ8G,
.collectionsCarousel_errorContainer__3BSK3 {
  text-align: center;
  padding: 4rem 2rem;
  color: #4b5563;
}

.collectionsCarousel_loadingSpinner__lVQO8 {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(37, 99, 235, 0.1);
  border-top: 3px solid #1e3a8a;
  border-radius: 50%;
  animation: collectionsCarousel_spin__9OrCb 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes collectionsCarousel_spin__9OrCb {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .collectionsCarousel_collectionsSection__PL2_N {
    padding: 4rem 0;
  }
  
  .collectionsCarousel_container__ojJCz {
    padding: 0 1.5rem;
  }
  
  .collectionsCarousel_title__WhpBj {
    font-size: 2.5rem;
  }
  
  .collectionsCarousel_collectionCard__cNhDx {
    flex: 0 0 280px;
    height: 350px;
  }
}

@media (max-width: 768px) {
  .collectionsCarousel_header__v5IST {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .collectionsCarousel_navigation__61e2M {
    align-self: flex-end;
  }

  .collectionsCarousel_title__WhpBj {
    font-size: 2rem;
  }

  .collectionsCarousel_subtitle__78z9Y {
    font-size: 1rem;
  }

  .collectionsCarousel_carousel__qCGVG {
    gap: 1.5rem; /* Increased gap between cards */
  }

  .collectionsCarousel_collectionCard__cNhDx {
    flex: 0 0 280px; /* Slightly wider */
    height: 380px; /* Increased height for better mobile experience */
  }

  .collectionsCarousel_cardContent__T3Io_ {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .collectionsCarousel_container__ojJCz {
    padding: 0 1rem;
  }

  .collectionsCarousel_header__v5IST {
    margin-bottom: 2rem;
  }

  .collectionsCarousel_navigation__61e2M {
    display: none;
  }

  .collectionsCarousel_carousel__qCGVG {
    gap: 1.25rem; /* Better gap for small screens */
  }

  .collectionsCarousel_collectionCard__cNhDx {
    flex: 0 0 260px; /* Increased width */
    height: 360px; /* Increased height for better mobile experience */
  }

  .collectionsCarousel_cardContent__T3Io_ {
    padding: 1.25rem;
  }

  .collectionsCarousel_collectionName__tYse8 {
    font-size: 1.25rem;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/Home/TestimonialsSection/testimonialsSection.module.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Testimonials Section Styles */
.testimonialsSection_testimonialsSection__Tzmqm {
  padding: 8rem 0;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

.testimonialsSection_testimonialsSection__Tzmqm::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/testimonials-pattern.svg') repeat;
  opacity: 0.05;
  z-index: 1;
}

.testimonialsSection_container__j23jT {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Header Styles */
.testimonialsSection_header__DWms1 {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.testimonialsSection_subtitle__R4kLF {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.15em;
  color: #white;
  margin-bottom: 1rem;
  display: block;
}

.testimonialsSection_title___8_aG {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.testimonialsSection_description___xTOo {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 1.125rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

/* Testimonials Container */
.testimonialsSection_testimonialsContainer__YsEH_ {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-bottom: 4rem;
}

/* Testimonial Content */
.testimonialsSection_testimonialContent__smF0f {
  position: relative;
}

.testimonialsSection_quoteIcon__nRr2b {
  color: #white;
  margin-bottom: 2rem;
  opacity: 0.7;
}

.testimonialsSection_testimonialText__m5Ip4 {
  position: relative;
}

.testimonialsSection_testimonialContent__smF0f {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.5rem;
  line-height: 1.6;
  color: #ffffff;
  margin-bottom: 2rem;
  font-style: italic;
  font-weight: 400;
}

.testimonialsSection_rating__zqobF {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1.5rem;
}

.testimonialsSection_projectInfo__dNlLf {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
}

.testimonialsSection_projectLabel__h12Nk {
  color: #white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.testimonialsSection_projectName__lCTWI {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

/* Testimonial Meta */
.testimonialsSection_testimonialMeta__BfXIQ {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.testimonialsSection_authorInfo__8VLhw {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.testimonialsSection_authorImage__gcB7h {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #white;
  flex-shrink: 0;
}

.testimonialsSection_authorPhoto__85FBe {
  object-fit: cover;
}

.testimonialsSection_authorDetails__oZrmT {
  flex: 1;
}

.testimonialsSection_authorName__iUZE2 {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
  line-height: 1.2;
}

.testimonialsSection_authorTitle__9wHVm {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.9rem;
  color: #white;
  font-weight: 500;
  margin-bottom: 0.125rem;
}

.testimonialsSection_authorCompany__nfmkj {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

/* Navigation */
.testimonialsSection_navigation___LX1G {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

.testimonialsSection_navDot__3_zvY {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.testimonialsSection_navDot__3_zvY:hover {
  background: rgba(212, 175, 140, 0.7);
  transform: scale(1.2);
}

.testimonialsSection_navDot__3_zvY.testimonialsSection_active__5TqxY {
  background: #white;
  transform: scale(1.3);
}

/* Stats */
.testimonialsSection_stats__V01Lt {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  padding-top: 3rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.testimonialsSection_stat__eXs9N {
  text-align: center;
}

.testimonialsSection_statNumber__5vyrK {
  display: block;
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #white;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.testimonialsSection_statLabel__nqpbm {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .testimonialsSection_testimonialsSection__Tzmqm {
    padding: 6rem 0;
  }
  
  .testimonialsSection_container__j23jT {
    padding: 0 1.5rem;
  }
  
  .testimonialsSection_title___8_aG {
    font-size: 3rem;
  }
  
  .testimonialsSection_testimonialsContainer__YsEH_ {
    gap: 3rem;
  }
  
  .testimonialsSection_testimonialContent__smF0f {
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .testimonialsSection_testimonialsSection__Tzmqm {
    padding: 4rem 0;
  }
  
  .testimonialsSection_header__DWms1 {
    margin-bottom: 3rem;
  }
  
  .testimonialsSection_title___8_aG {
    font-size: 2.5rem;
  }
  
  .testimonialsSection_description___xTOo {
    font-size: 1rem;
  }
  
  .testimonialsSection_testimonialsContainer__YsEH_ {
    grid-template-columns: 1fr;
    gap: 2.5rem;
    text-align: center;
  }
  
  .testimonialsSection_testimonialContent__smF0f {
    font-size: 1.125rem;
  }
  
  .testimonialsSection_authorInfo__8VLhw {
    justify-content: center;
  }
  
  .testimonialsSection_stats__V01Lt {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .testimonialsSection_container__j23jT {
    padding: 0 1rem;
  }
  
  .testimonialsSection_header__DWms1 {
    margin-bottom: 2rem;
  }
  
  .testimonialsSection_title___8_aG {
    font-size: 2rem;
  }
  
  .testimonialsSection_testimonialContent__smF0f {
    font-size: 1rem;
  }
  
  .testimonialsSection_authorInfo__8VLhw {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .testimonialsSection_authorImage__gcB7h {
    width: 60px;
    height: 60px;
  }
  
  .testimonialsSection_stats__V01Lt {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .testimonialsSection_statNumber__5vyrK {
    font-size: 2rem;
  }
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/Home/homeComponent.module.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/* Home Component Styles */
.homeComponent_homeComponent__DXTGb {
  min-height: 100vh;
  background: #ffffff;
  overflow-x: hidden;
}

/* Smooth scrolling for the entire page */
.homeComponent_homeComponent__DXTGb {
  scroll-behavior: smooth;
}

/* Ensure proper spacing between sections */
.homeComponent_homeComponent__DXTGb > * {
  position: relative;
  z-index: 1;
}

/* Add subtle transitions for section reveals */
.homeComponent_homeComponent__DXTGb section {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .homeComponent_homeComponent__DXTGb {
    overflow-x: hidden;
  }
}

/* Print styles */
@media print {
  .homeComponent_homeComponent__DXTGb {
    background: white;
    color: black;
  }
}

